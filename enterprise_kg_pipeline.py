#!/usr/bin/env python3
"""
Enterprise Knowledge Graph Pipeline

This script orchestrates the complete flow from input file to knowledge graph:
1. Extract and categorize text from input file using text_caateg.py
2. Extract relationships from categorized data using entity_relationships.py
3. Build knowledge graph in Neo4j with a company node as the root

Usage:
    python enterprise_kg_pipeline.py --input path/to/input.pdf --source pdf --company "Acme Corp"
"""

import os
import sys
import json
import argparse
import tempfile
from typing import Dict, List, Any, Optional
import logging
from pathlib import Path
from dotenv import load_dotenv
import openai # For OpenAI embeddings

# Load environment variables from .env file
load_dotenv()

# Import from other modules
try:
    # Import text categorization module
    from text_caateg import extract_text_from_source, process_text
    
    # Import relationship extraction module
    from entity_relationships import extract_relationships_from_entities, get_relationship_types
    
    # Import entity categories
    from entity_categories import get_categories_for_entity_type, get_likely_entity_types
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print("Make sure text_caateg.py, entity_relationships.py, and entity_categories.py are in the same directory.")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Neo4j credentials - hardcoded as requested
NEO4J_URI = "bolt://localhost:7687"
NEO4J_USERNAME = "neo4j"
NEO4J_PASSWORD = "password"  # Change this to your actual password

# Embedding model to use
EMBEDDING_MODEL = "text-embedding-ada-002"

class EnterpriseKnowledgeGraphBuilder:
    """
    A class to build an enterprise knowledge graph in Neo4j from entity data.
    """
    
    def __init__(self, uri: str = NEO4J_URI, username: str = NEO4J_USERNAME, 
                 password: str = NEO4J_PASSWORD, embedding_model: str = EMBEDDING_MODEL):
        """
        Initialize the knowledge graph builder.
        
        Args:
            uri: Neo4j database URI
            username: Neo4j username
            password: Neo4j password
            embedding_model: Name of the sentence transformer model for embeddings
        """
        try:
            from neo4j import GraphDatabase
            # sentence_transformers import removed, openai imported globally
            
            self.driver = GraphDatabase.driver(uri, auth=(username, password))
            logger.info(f"Connected to Neo4j database at {uri}")

            # Initialize OpenAI client for embeddings
            # Ensure OPENAI_API_KEY is set in environment variables (loaded by load_dotenv())
            if not os.getenv("OPENAI_API_KEY"):
                logger.error("OPENAI_API_KEY environment variable not set. Please set it in your .env file or environment.")
                raise ValueError("OPENAI_API_KEY environment variable not set.")
            
            self.openai_client = openai.OpenAI()
            self.embedding_model_name = embedding_model # Store the model name passed (e.g., "text-embedding-ada-002")
            logger.info(f"Using OpenAI embedding model: {self.embedding_model_name}")
            
            # Verify connection
            self._verify_connection()
            
        except ImportError as e: # Catch if neo4j is not installed
            logger.error(f"Required neo4j package not installed or other import error: {e}")
            raise
        except ValueError as ve: # Catch our specific OPENAI_API_KEY error
            logger.error(str(ve))
            raise
        except Exception as e:
            logger.error(f"Error initializing knowledge graph builder: {e}")
            raise
    
    def _verify_connection(self):
        """Verify connection to Neo4j database."""
        try:
            with self.driver.session() as session:
                result = session.run("RETURN 1 AS num")
                assert result.single()["num"] == 1
                logger.info("Neo4j connection verified successfully")
        except Exception as e:
            logger.error(f"Failed to connect to Neo4j: {e}")
            raise
    
    def close(self):
        """Close the Neo4j driver connection."""
        self.driver.close()
        logger.info("Neo4j connection closed")
    
    def clear_database(self):
        """Clear all nodes and relationships in the database."""
        with self.driver.session() as session:
            session.run("MATCH (n) DETACH DELETE n")
        logger.info("Database cleared")
    
    def get_embedding(self, text: str) -> List[float]:
        """
        Generate an embedding for the given text using OpenAI.
        
        Args:
            text: Text to embed
            
        Returns:
            Embedding vector, or a zero vector of 1536 dimensions on error or empty input.
        """
        # OpenAI API requires non-empty input and recommends replacing newlines.
        # text-embedding-ada-002 model dimension is 1536.
        embedding_dimension = 1536

        if not text or not text.strip():
            logger.warning(f"Empty or whitespace-only text provided for embedding, returning zero vector of dimension {embedding_dimension}.")
            return [0.0] * embedding_dimension
        
        try:
            text_to_embed = text.replace("\n", " ")
            response = self.openai_client.embeddings.create(
                input=[text_to_embed],  # API expects a list of strings
                model=self.embedding_model_name
            )
            return response.data[0].embedding
        except openai.APIError as api_err: # More specific error handling for OpenAI
            logger.error(f"OpenAI API error while getting embedding for text '{text[:50]}...': {api_err}")
            return [0.0] * embedding_dimension
        except Exception as e:
            logger.error(f"Unexpected error getting embedding from OpenAI for text '{text[:50]}...': {e}")
            return [0.0] * embedding_dimension
    
    def create_company_node(self, company_name: str, company_description: str = "") -> int:
        """
        Create the root company node.
        
        Args:
            company_name: Name of the company
            company_description: Description of the company
            
        Returns:
            ID of the created node
        """
        with self.driver.session() as session:
            result = session.run(
                """
                MERGE (c:Company {name: $name})
                SET c.description = $description,
                    c.title_embedding = $title_embedding,
                    c.description_embedding = $description_embedding
                RETURN id(c) AS node_id
                """,
                name=company_name,
                description=company_description or f"Company: {company_name}",
                title_embedding=self.get_embedding(company_name),
                description_embedding=self.get_embedding(company_description or f"Company: {company_name}")
            )
            node_id = result.single()["node_id"]
            logger.info(f"Created company node: {company_name} (ID: {node_id})")
            return node_id
    
    def create_category_nodes(self, categories: List[Dict[str, str]], company_id: int) -> Dict[str, int]:
        """
        Create category nodes and link them to the company node.
        
        Args:
            categories: List of category dictionaries with name and description
            company_id: ID of the company node
            
        Returns:
            Dictionary mapping category names to their node IDs
        """
        category_ids = {}
        with self.driver.session() as session:
            for category in categories:
                name = category["name"]
                description = category.get("description", f"Category: {name}")
                
                result = session.run(
                    """
                    MERGE (c:Category {name: $name})
                    SET c.description = $description,
                        c.title_embedding = $title_embedding,
                        c.description_embedding = $description_embedding
                    WITH c
                    MATCH (company) WHERE id(company) = $company_id
                    MERGE (company)-[:HAS_CATEGORY]->(c)
                    RETURN id(c) AS node_id
                    """,
                    name=name,
                    description=description,
                    title_embedding=self.get_embedding(name),
                    description_embedding=self.get_embedding(description),
                    company_id=company_id
                )
                node_id = result.single()["node_id"]
                category_ids[name] = node_id
                logger.info(f"Created category node: {name} (ID: {node_id})")
            
        return category_ids
    
    def create_entity_nodes(self, entities: List[Dict[str, Any]], category_ids: Dict[str, int]) -> Dict[str, int]:
        """
        Create entity nodes and link them to their categories.
        
        Args:
            entities: List of entity dictionaries
            category_ids: Dictionary mapping category names to their node IDs
            
        Returns:
            Dictionary mapping entity names to their node IDs
        """
        entity_ids = {}
        with self.driver.session() as session:
            for entity in entities:
                name = entity["name"]
                entity_type = entity.get("entity_type", "Generic")
                category = entity.get("category", "Miscellaneous")
                description = entity.get("description", f"{entity_type}: {name}")
                attributes = entity.get("attributes", {})
                
                # Sanitize attributes (ensure all values are strings)
                sanitized_attributes = {k: str(v) if v is not None else "" for k, v in attributes.items()}
                
                # Add entity type as a property
                sanitized_attributes["entity_type"] = entity_type
                
                # Get category ID, default to Miscellaneous if not found
                category_id = category_ids.get(category)
                if not category_id and "Miscellaneous" in category_ids:
                    category_id = category_ids["Miscellaneous"]
                    category = "Miscellaneous"
                    logger.warning(f"Category not found for entity {name}, using Miscellaneous")
                
                if not category_id:
                    logger.error(f"Cannot create entity {name}: no valid category found")
                    continue
                
                result = session.run(
                    """
                    MERGE (e:Entity {name: $name})
                    SET e += $attributes,
                        e.description = $description,
                        e.category = $category,
                        e.title_embedding = $title_embedding,
                        e.description_embedding = $description_embedding
                    WITH e
                    MATCH (c:Category) WHERE id(c) = $category_id
                    MERGE (c)-[:HAS_ENTITY]->(e)
                    RETURN id(e) AS node_id
                    """,
                    name=name,
                    attributes=sanitized_attributes,
                    description=description,
                    category=category,
                    title_embedding=self.get_embedding(name),
                    description_embedding=self.get_embedding(description),
                    category_id=category_id
                )
                node_id = result.single()["node_id"]
                entity_ids[name] = node_id
                logger.info(f"Created entity node: {name} (ID: {node_id})")
            
        return entity_ids
    
    def create_relationships(self, relationships: List[Dict[str, Any]], entity_ids: Dict[str, int]):
        """
        Create relationships between entities.
        
        Args:
            relationships: List of relationship dictionaries
            entity_ids: Dictionary mapping entity names to their node IDs
        """
        with self.driver.session() as session:
            for rel in relationships:
                source = rel["source"]
                target = rel["target"]
                rel_type = rel["relationship"]
                properties = rel.get("properties", {})
                
                # Skip if source or target entity doesn't exist
                if source not in entity_ids or target not in entity_ids:
                    logger.warning(f"Skipping relationship {source}-[{rel_type}]->{target}: entities not found")
                    continue
                
                # Sanitize properties
                sanitized_properties = {k: str(v) if v is not None else "" for k, v in properties.items()}
                
                session.run(
                    f"""
                    MATCH (s:Entity) WHERE id(s) = $source_id
                    MATCH (t:Entity) WHERE id(t) = $target_id
                    MERGE (s)-[r:`{rel_type}`]->(t)
                    SET r += $properties
                    """,
                    source_id=entity_ids[source],
                    target_id=entity_ids[target],
                    properties=sanitized_properties
                )
                logger.info(f"Created relationship: {source}-[{rel_type}]->{target}")
    
    def build_knowledge_graph(self, data: Dict[str, Any], company_name: str, company_description: str = ""):
        """
        Build the complete knowledge graph from the provided data.
        
        Args:
            data: Dictionary containing entities, categories, and relationships
            company_name: Name of the company
            company_description: Description of the company
        """
        # Extract components from data
        entities = data.get("entities", [])
        categories = data.get("categories", [])
        relationships = data.get("relationships", [])
        
        # Ensure we have a Miscellaneous category
        if not any(cat["name"] == "Miscellaneous" for cat in categories):
            categories.append({
                "name": "Miscellaneous",
                "description": "Information that doesn't fit into other categories"
            })
        
        # Create company node
        company_id = self.create_company_node(company_name, company_description)
        
        # Create category nodes
        category_ids = self.create_category_nodes(categories, company_id)
        
        # Create entity nodes
        entity_ids = self.create_entity_nodes(entities, category_ids)
        
        # Create relationships
        self.create_relationships(relationships, entity_ids)
        
        logger.info(f"Knowledge graph built successfully with {len(entities)} entities, "
                   f"{len(categories)} categories, and {len(relationships)} relationships")

def extract_implicit_relationships(entities: List[Dict[str, Any]], data_source: str) -> List[Dict[str, Any]]:
    """
    Extract implicit relationships from entity data that aren't covered by explicit rules.
    
    Args:
        entities: List of entity dictionaries
        data_source: The source of the data
        
    Returns:
        A list of relationship dictionaries
    """
    relationships = []
    
    # Create a lookup dictionary for entities by type and id
    entity_lookup = {}
    for entity_data in entities:
        entity = entity_data.get("entity", {})
        entity_type = entity.get("type")
        entity_id = entity.get("id")
        if entity_type and entity_id:
            if entity_type not in entity_lookup:
                entity_lookup[entity_type] = {}
            entity_lookup[entity_type][entity_id] = entity_data
    
    # Extract relationships based on categories and content
    for entity_data in entities:
        entity = entity_data.get("entity", {})
        entity_id = entity.get("id")
        entity_type = entity.get("type")
        categories = entity_data.get("categories", {})
        
        if not entity_id or not entity_type:
            continue
        
        # Look for mentions of other entities in category content
        for category_name, category_content in categories.items():
            if not category_content:
                continue
                
            # Skip certain categories that are less likely to contain relationship information
            if category_name in ["Miscellaneous", "Timeline"]:
                continue
                
            # Check if category content mentions other entities
            for other_type, other_entities in entity_lookup.items():
                if other_type == entity_type and entity_id == entity_id:
                    continue  # Skip self-references
                    
                for other_id, other_entity_data in other_entities.items():
                    # Skip if the entity is already the source
                    if other_id == entity_id:
                        continue
                        
                    # Check if the other entity is mentioned in the category content
                    if isinstance(category_content, str) and other_id in category_content:
                        # Determine relationship type based on category
                        relationship_type = "RELATED_TO"  # Default
                        
                        if category_name == "Contacts/Relationships" or category_name == "Team Members":
                            relationship_type = "KNOWS"
                        elif category_name == "Projects" or category_name == "Objectives":
                            relationship_type = "INVOLVED_IN"
                        elif category_name == "Work/Professional" or category_name == "Leadership":
                            relationship_type = "WORKS_WITH"
                        elif category_name == "Assignee" or category_name == "Reporter":
                            relationship_type = "ASSIGNED_TO"
                        
                        relationships.append({
                            "source": entity_id,
                            "target": other_id,
                            "relationship": relationship_type,
                            "properties": {
                                "data_source": data_source,
                                "derived_from": category_name
                            }
                        })
    
    return relationships

def process_file_to_knowledge_graph(
    input_file: str,
    source_type: str,
    company_name: str,
    company_description: str = "",
    clear_graph: bool = False,
    output_dir: Optional[str] = None
) -> bool:
    """
    Process an input file through the complete pipeline to build a knowledge graph.
    
    Args:
        input_file: Path to the input file
        source_type: Type of data source (pdf, slack, jira, etc.)
        company_name: Name of the company (root node)
        company_description: Description of the company
        clear_graph: Whether to clear the existing graph before building
        output_dir: Directory to save intermediate files (if None, uses temp directory)
        
    Returns:
        True if successful, False otherwise
    """
    try:
        # Create output directory if specified
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
            temp_dir = output_dir
            delete_temp = False
        else:
            temp_dir = tempfile.mkdtemp()
            delete_temp = True
            
        logger.info(f"Using temporary directory: {temp_dir}")
        
        # Step 1: Extract text from file
        logger.info(f"Extracting text from {source_type} source: {input_file}")
        text = extract_text_from_source(source_type, input_file)
        
        if not text:
            logger.error(f"Failed to extract text from the {source_type} source.")
            return False
            
        logger.info(f"Extracted {len(text)} characters of text.")
        
        # Step 2: Process text to extract entities and categorize them
        logger.info(f"Processing text from {source_type} source...")
        categorized_data = process_text(source_type, text)
        
        # Save categorized data to file
        categorized_file = os.path.join(temp_dir, "categorized_data.json")
        with open(categorized_file, 'w') as f:
            json.dump(categorized_data, f, indent=2)
        logger.info(f"Saved categorized data to {categorized_file}")
        
        # Step 3: Extract relationships
        logger.info("Extracting relationships from categorized data")
        entities = categorized_data.get("entities", [])
        data_source = categorized_data.get("data_source", source_type)
        
        # Extract explicit relationships
        explicit_relationships = extract_relationships_from_entities(entities, data_source)
        logger.info(f"Extracted {len(explicit_relationships)} explicit relationships")
        
        # Extract implicit relationships
        implicit_relationships = extract_implicit_relationships(entities, data_source)
        logger.info(f"Extracted {len(implicit_relationships)} implicit relationships")
        
        # Combine relationships
        all_relationships = explicit_relationships + implicit_relationships
        
        # Save relationships to file
        relationships_file = os.path.join(temp_dir, "relationships.json")
        relationship_data = {
            "status": "success",
            "data_source": data_source,
            "relationships": all_relationships
        }
        with open(relationships_file, 'w') as f:
            json.dump(relationship_data, f, indent=2)
        logger.info(f"Saved relationships to {relationships_file}")
        
        # Step 4: Build knowledge graph
        logger.info("Building knowledge graph")
        
        # Initialize knowledge graph builder
        graph_builder = EnterpriseKnowledgeGraphBuilder()
        
        # Clear graph if requested
        if clear_graph:
            logger.info("Clearing existing graph")
            graph_builder.clear_database()
        
        # Prepare data for graph builder
        graph_data = {
            "entities": [],
            "categories": [],
            "relationships": []
        }
        
        # Add entities
        for entity_data in entities:
            entity = entity_data.get("entity", {})
            entity_type = entity.get("type")
            entity_id = entity.get("id")
            attributes = entity.get("attributes", {})
            categories = entity_data.get("categories", {})
            
            if not entity_id or not entity_type:
                continue
            
            # Determine category
            primary_category = None
            for cat_name, cat_content in categories.items():
                if cat_content and cat_name != "Miscellaneous":
                    primary_category = cat_name
                    break
            
            # Create description from categories
            description = ""
            for cat_name, cat_content in categories.items():
                if cat_content and isinstance(cat_content, str):
                    description += f"{cat_name}: {cat_content}\n"
            
            graph_data["entities"].append({
                "name": entity_id,
                "entity_type": entity_type,
                "category": primary_category or "Miscellaneous",
                "description": description.strip(),
                "attributes": attributes
            })
        
        # Add categories
        unique_categories = set()
        for entity in graph_data["entities"]:
            category = entity.get("category")
            if category:
                unique_categories.add(category)
        
        for category in unique_categories:
            graph_data["categories"].append({
                "name": category,
                "description": f"Category for {category} information"
            })
        
        # Add relationships
        for rel in all_relationships:
            graph_data["relationships"].append({
                "source": rel.get("source"),
                "target": rel.get("target"),
                "relationship": rel.get("relationship"),
                "properties": rel.get("properties", {})
            })
        
        # Build the graph
        graph_builder.build_knowledge_graph(graph_data, company_name, company_description)
        
        # Close the graph connection
        graph_builder.close()
        
        logger.info("Knowledge graph built successfully")
        
        # Clean up temporary directory if created
        if delete_temp:
            import shutil
            shutil.rmtree(temp_dir)
            
        return True
        
    except Exception as e:
        logger.error(f"Error in knowledge graph pipeline: {e}")
        return False

def main():
    """Main function to run the script."""
    parser = argparse.ArgumentParser(description="Process a file into a knowledge graph")
    parser.add_argument("--input", required=True, help="Path to the input file")
    parser.add_argument("--source", required=True, help="Source type (pdf, slack, jira, gdrive, etc.)")
    parser.add_argument("--company", required=True, help="Name of the company (root node)")
    parser.add_argument("--description", default="", help="Description of the company")
    parser.add_argument("--clear", action="store_true", help="Clear the database before building")
    parser.add_argument("--output-dir", help="Directory to save intermediate files")
    
    args = parser.parse_args()
    
    success = process_file_to_knowledge_graph(
        args.input,
        args.source,
        args.company,
        args.description,
        args.clear,
        args.output_dir
    )
    
    if success:
        logger.info("Knowledge graph pipeline completed successfully")
        sys.exit(0)
    else:
        logger.error("Knowledge graph pipeline failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
