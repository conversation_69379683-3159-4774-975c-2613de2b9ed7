#!/usr/bin/env python3
"""
Knowledge Graph Pipeline

This script orchestrates the complete flow from input file to knowledge graph:
1. Extract and categorize text from input file
2. Extract relationships from categorized data
3. Build knowledge graph in Neo4j

Usage:
    python knowledge_graph_pipeline.py --input path/to/input.pdf --company "Acme Corp"
"""

import os
import sys
import json
import argparse
import tempfile
from typing import Dict, List, Any, Optional
import logging
from pathlib import Path
import PyPDF2
import docx
import pandas as pd

# Import from other modules
try:
    from text_caateg import extract_and_categorize_text
    from entity_relationships import extract_relationships_from_entities, get_relationship_types
    from enterprise_knowledge_graph_builder import EnterpriseKnowledgeGraphBuilder
except ImportError:
    print("Error: Required modules not found. Make sure text_caateg.py, entity_relationships.py, and enterprise_knowledge_graph_builder.py are in the same directory.")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Neo4j credentials - hardcoded as requested
NEO4J_URI = "bolt://localhost:7687"
NEO4J_USERNAME = "neo4j"
NEO4J_PASSWORD = "password"  # Change this to your actual password

# Embedding model to use
EMBEDDING_MODEL = "all-MiniLM-L6-v2"

def extract_text_from_file(file_path: str) -> str:
    """
    Extract text from various file types (PDF, DOCX, TXT, CSV, XLSX).
    
    Args:
        file_path: Path to the input file
        
    Returns:
        Extracted text content
    """
    file_extension = Path(file_path).suffix.lower()
    
    try:
        # PDF files
        if file_extension == '.pdf':
            text = ""
            with open(file_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                for page_num in range(len(reader.pages)):
                    page = reader.pages[page_num]
                    text += page.extract_text() + "\n"
            return text
            
        # Word documents
        elif file_extension == '.docx':
            doc = docx.Document(file_path)
            return "\n".join([paragraph.text for paragraph in doc.paragraphs])
            
        # Text files
        elif file_extension == '.txt':
            with open(file_path, 'r', encoding='utf-8') as file:
                return file.read()
                
        # CSV files
        elif file_extension == '.csv':
            df = pd.read_csv(file_path)
            return df.to_string()
            
        # Excel files
        elif file_extension in ['.xlsx', '.xls']:
            df = pd.read_excel(file_path)
            return df.to_string()
            
        # JSON files
        elif file_extension == '.json':
            with open(file_path, 'r', encoding='utf-8') as file:
                data = json.load(file)
                return json.dumps(data, indent=2)
                
        else:
            raise ValueError(f"Unsupported file type: {file_extension}")
            
    except Exception as e:
        logger.error(f"Error extracting text from file: {e}")
        return ""

def load_json_file(file_path: str) -> Dict:
    """
    Load a JSON file.
    
    Args:
        file_path: Path to the JSON file
        
    Returns:
        Loaded JSON data as a dictionary
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return json.load(file)
    except Exception as e:
        logger.error(f"Error loading JSON file: {e}")
        return {}

def save_json_file(data: Dict, file_path: str) -> bool:
    """
    Save data to a JSON file.
    
    Args:
        data: Data to save
        file_path: Path to save the JSON file
        
    Returns:
        True if successful, False otherwise
    """
    try:
        with open(file_path, 'w', encoding='utf-8') as file:
            json.dump(data, file, indent=2)
        return True
    except Exception as e:
        logger.error(f"Error saving JSON file: {e}")
        return False

def extract_implicit_relationships(entities: List[Dict[str, Any]], data_source: str) -> List[Dict[str, Any]]:
    """
    Extract implicit relationships from entity data that aren't covered by explicit rules.
    
    Args:
        entities: List of entity dictionaries
        data_source: The source of the data
        
    Returns:
        A list of relationship dictionaries
    """
    relationships = []
    
    # Create a lookup dictionary for entities by type and id
    entity_lookup = {}
    for entity in entities:
        entity_type = entity.get("entity", {}).get("type")
        entity_id = entity.get("entity", {}).get("id")
        if entity_type and entity_id:
            if entity_type not in entity_lookup:
                entity_lookup[entity_type] = {}
            entity_lookup[entity_type][entity_id] = entity
    
    # Extract relationships based on categories and content
    for entity in entities:
        entity_id = entity.get("entity", {}).get("id")
        entity_type = entity.get("entity", {}).get("type")
        categories = entity.get("categories", {})
        
        # Look for mentions of other entities in category content
        for category_name, category_content in categories.items():
            if not category_content:
                continue
                
            # Skip certain categories that are less likely to contain relationship information
            if category_name in ["Miscellaneous", "Timeline"]:
                continue
                
            # Check if category content mentions other entities
            for other_type, other_entities in entity_lookup.items():
                if other_type == entity_type and other_entities == entity_id:
                    continue  # Skip self-references
                    
                for other_id, other_entity in other_entities.items():
                    # Skip if the entity is already the source
                    if other_id == entity_id:
                        continue
                        
                    # Check if the other entity is mentioned in the category content
                    if isinstance(category_content, str) and other_id in category_content:
                        # Determine relationship type based on category
                        relationship_type = "RELATED_TO"  # Default
                        
                        if category_name == "Contacts/Relationships":
                            relationship_type = "KNOWS"
                        elif category_name == "Projects":
                            relationship_type = "INVOLVED_IN"
                        elif category_name == "Work/Professional":
                            relationship_type = "WORKS_WITH"
                        
                        relationships.append({
                            "source": entity_id,
                            "target": other_id,
                            "relationship": relationship_type,
                            "properties": {
                                "data_source": data_source,
                                "derived_from": category_name
                            }
                        })
    
    return relationships

def enrich_relationships(relationships: List[Dict[str, Any]], entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Enrich relationship data with additional properties based on entity data.
    
    Args:
        relationships: List of relationship dictionaries
        entities: List of entity dictionaries
        
    Returns:
        Enriched list of relationship dictionaries
    """
    # Create a lookup dictionary for entities by id
    entity_lookup = {}
    for entity in entities:
        entity_id = entity.get("entity", {}).get("id")
        if entity_id:
            entity_lookup[entity_id] = entity
    
    enriched_relationships = []
    for rel in relationships:
        source_id = rel.get("source")
        target_id = rel.get("target")
        relationship_type = rel.get("relationship")
        
        if source_id in entity_lookup and target_id in entity_lookup:
            source_entity = entity_lookup[source_id]
            target_entity = entity_lookup[target_id]
            source_type = source_entity.get("entity", {}).get("type")
            target_type = target_entity.get("entity", {}).get("type")
            
            # Copy the original relationship
            enriched_rel = rel.copy()
            
            # Add source and target entity types
            enriched_rel["source_type"] = source_type
            enriched_rel["target_type"] = target_type
            
            # Add source and target entity names for readability
            source_name = source_entity.get("entity", {}).get("attributes", {}).get("name", source_id)
            if source_type == "Person":
                source_name = source_id  # For Person entities, the ID is often the name
            
            target_name = target_entity.get("entity", {}).get("attributes", {}).get("name", target_id)
            if target_type == "Person":
                target_name = target_id  # For Person entities, the ID is often the name
            
            enriched_rel["source_name"] = source_name
            enriched_rel["target_name"] = target_name
            
            enriched_relationships.append(enriched_rel)
        else:
            # If source or target entity not found, just add the original
            enriched_relationships.append(rel)
    
    return enriched_relationships

def process_file_to_knowledge_graph(
    input_file: str,
    company_name: str,
    company_description: str = "",
    clear_graph: bool = False,
    output_dir: Optional[str] = None
) -> bool:
    """
    Process an input file through the complete pipeline to build a knowledge graph.
    
    Args:
        input_file: Path to the input file
        company_name: Name of the company (root node)
        company_description: Description of the company
        clear_graph: Whether to clear the existing graph before building
        output_dir: Directory to save intermediate files (if None, uses temp directory)
        
    Returns:
        True if successful, False otherwise
    """
    try:
        # Create output directory if specified
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
            temp_dir = output_dir
            delete_temp = False
        else:
            temp_dir = tempfile.mkdtemp()
            delete_temp = True
            
        logger.info(f"Using temporary directory: {temp_dir}")
        
        # Step 1: Extract text from file if not already JSON
        file_extension = Path(input_file).suffix.lower()
        
        if file_extension == '.json':
            # If input is already JSON, check if it's categorized data
            categorized_data = load_json_file(input_file)
            if "entities" in categorized_data:
                logger.info("Input file is already categorized JSON data")
                categorized_file = input_file
            else:
                logger.info("Input file is JSON but not categorized, running categorization")
                text = json.dumps(categorized_data, indent=2)
                categorized_file = os.path.join(temp_dir, "categorized_data.json")
                categorized_data = extract_and_categorize_text(text, company_name)
                save_json_file(categorized_data, categorized_file)
        else:
            # Extract text from file
            logger.info(f"Extracting text from {input_file}")
            text = extract_text_from_file(input_file)
            if not text:
                logger.error("Failed to extract text from input file")
                return False
                
            # Step 2: Categorize text
            logger.info("Categorizing extracted text")
            categorized_file = os.path.join(temp_dir, "categorized_data.json")
            categorized_data = extract_and_categorize_text(text, company_name)
            save_json_file(categorized_data, categorized_file)
        
        # Step 3: Extract relationships
        logger.info("Extracting relationships from categorized data")
        entities = categorized_data.get("entities", [])
        data_source = categorized_data.get("data_source", "unknown")
        
        # Extract explicit relationships
        explicit_relationships = extract_relationships_from_entities(entities, data_source)
        logger.info(f"Extracted {len(explicit_relationships)} explicit relationships")
        
        # Extract implicit relationships
        implicit_relationships = extract_implicit_relationships(entities, data_source)
        logger.info(f"Extracted {len(implicit_relationships)} implicit relationships")
        
        # Combine relationships
        all_relationships = explicit_relationships + implicit_relationships
        
        # Enrich relationships
        enriched_relationships = enrich_relationships(all_relationships, entities)
        logger.info(f"Enriched {len(enriched_relationships)} relationships")
        
        # Save relationships to file
        relationships_file = os.path.join(temp_dir, "relationships.json")
        relationship_data = {
            "status": "success",
            "data_source": data_source,
            "relationships": enriched_relationships
        }
        save_json_file(relationship_data, relationships_file)
        
        # Step 4: Build knowledge graph
        logger.info("Building knowledge graph")
        
        # Initialize knowledge graph builder
        graph_builder = EnterpriseKnowledgeGraphBuilder(
            uri=NEO4J_URI,
            username=NEO4J_USERNAME,
            password=NEO4J_PASSWORD,
            embedding_model=EMBEDDING_MODEL
        )
        
        # Clear graph if requested
        if clear_graph:
            logger.info("Clearing existing graph")
            graph_builder.clear_database()
        
        # Prepare data for graph builder
        graph_data = {
            "entities": [],
            "categories": [],
            "relationships": []
        }
        
        # Add entities
        for entity in entities:
            entity_data = entity.get("entity", {})
            entity_type = entity_data.get("type")
            entity_id = entity_data.get("id")
            attributes = entity_data.get("attributes", {})
            categories = entity.get("categories", {})
            
            # Determine category
            category = None
            for cat_name, cat_content in categories.items():
                if cat_content and cat_name != "Miscellaneous":
                    category = cat_name
                    break
            
            # Create description from categories
            description = ""
            for cat_name, cat_content in categories.items():
                if cat_content and isinstance(cat_content, str):
                    description += f"{cat_name}: {cat_content}\n"
            
            graph_data["entities"].append({
                "name": entity_id,
                "entity_type": entity_type,
                "category": category or "Miscellaneous",
                "description": description.strip(),
                "attributes": attributes
            })
        
        # Add categories
        unique_categories = set()
        for entity in graph_data["entities"]:
            category = entity.get("category")
            if category:
                unique_categories.add(category)
        
        for category in unique_categories:
            graph_data["categories"].append({
                "name": category,
                "description": f"Category for {category} information"
            })
        
        # Add relationships
        for rel in enriched_relationships:
            graph_data["relationships"].append({
                "source": rel.get("source"),
                "target": rel.get("target"),
                "relationship": rel.get("relationship"),
                "properties": rel.get("properties", {})
            })
        
        # Build the graph
        graph_builder.build_knowledge_graph(graph_data, company_name, company_description)
        
        # Close the graph connection
        graph_builder.close()
        
        logger.info("Knowledge graph built successfully")
        
        # Clean up temporary directory if created
        if delete_temp:
            import shutil
            shutil.rmtree(temp_dir)
            
        return True
        
    except Exception as e:
        logger.error(f"Error in knowledge graph pipeline: {e}")
        return False

def main():
    """Main function to run the script."""
    parser = argparse.ArgumentParser(description="Process a file into a knowledge graph")
    parser.add_argument("--input", required=True, help="Path to the input file")
    parser.add_argument("--company", required=True, help="Name of the company (root node)")
    parser.add_argument("--description", default="", help="Description of the company")
    parser.add_argument("--clear", action="store_true", help="Clear the database before building")
    parser.add_argument("--output-dir", help="Directory to save intermediate files")
    
    args = parser.parse_args()
    
    success = process_file_to_knowledge_graph(
        args.input,
        args.company,
        args.description,
        args.clear,
        args.output_dir
    )
    
    if success:
        logger.info("Knowledge graph pipeline completed successfully")
        sys.exit(0)
    else:
        logger.error("Knowledge graph pipeline failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
