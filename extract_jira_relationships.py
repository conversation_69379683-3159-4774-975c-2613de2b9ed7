#!/usr/bin/env python3
"""
Extract Jira Relationships

This script extracts relationships from a Jira categorized JSON file using the
relationship definitions in entity_relationships.py.

Usage:
    python extract_jira_relationships.py --input JiraSource_categorized.json --output jira_relationships.json
"""

import os
import sys
import json
import argparse
from typing import Dict, List, Any
import logging
from entity_relationships import extract_relationships_from_entities, get_relationship_types

# Configure logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def extract_explicit_relationships(entities: List[Dict[str, Any]], data_source: str) -> List[Dict[str, Any]]:
    """
    Extract relationships based on explicit rules defined in entity_relationships.py.
    
    Args:
        entities: List of entity dictionaries
        data_source: The source of the data
        
    Returns:
        A list of relationship dictionaries
    """
    return extract_relationships_from_entities(entities, data_source)

def extract_implicit_relationships(entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Extract implicit relationships from entity data that aren't covered by explicit rules.
    
    Args:
        entities: List of entity dictionaries
        
    Returns:
        A list of relationship dictionaries
    """
    relationships = []
    
    # Create a lookup dictionary for entities by type and id
    entity_lookup = {}
    for entity in entities:
        entity_type = entity.get("entity", {}).get("type")
        entity_id = entity.get("entity", {}).get("id")
        if entity_type and entity_id:
            if entity_type not in entity_lookup:
                entity_lookup[entity_type] = {}
            entity_lookup[entity_type][entity_id] = entity
    
    # Extract JiraIssue to Person relationships from comments
    for entity in entities:
        if entity.get("entity", {}).get("type") == "JiraIssue":
            issue_id = entity.get("entity", {}).get("id")
            comments = entity.get("categories", {}).get("Comments", "")
            
            if comments and isinstance(comments, str):
                # Extract commenters from comments text
                comment_parts = comments.split("Comment ")
                for part in comment_parts[1:]:  # Skip the first empty part
                    if " by " in part and " on " in part:
                        commenter_name = part.split(" by ")[1].split(" on ")[0].strip()
                        timestamp = part.split(" on ")[1].split(":")[0].strip()
                        
                        # Check if commenter exists as a Person entity
                        if "Person" in entity_lookup and commenter_name in entity_lookup["Person"]:
                            relationships.append({
                                "source": commenter_name,
                                "target": issue_id,
                                "relationship": "COMMENTED_ON",
                                "properties": {
                                    "timestamp": timestamp,
                                    "data_source": "jira"
                                }
                            })
    
    # Extract relationships between JiraIssues based on "Related Issues" category
    for entity in entities:
        if entity.get("entity", {}).get("type") == "JiraIssue":
            issue_id = entity.get("entity", {}).get("id")
            related_issues = entity.get("categories", {}).get("Related Issues", "")
            
            if related_issues and isinstance(related_issues, str):
                # Extract related issue keys
                for related_key in related_issues.split(","):
                    related_key = related_key.strip()
                    if related_key:
                        # Find the related issue entity
                        related_entity = None
                        for e in entities:
                            if (e.get("entity", {}).get("type") == "JiraIssue" and 
                                e.get("entity", {}).get("attributes", {}).get("Issue Key") == related_key):
                                related_entity = e
                                break
                        
                        if related_entity:
                            related_id = related_entity.get("entity", {}).get("id")
                            relationships.append({
                                "source": issue_id,
                                "target": related_id,
                                "relationship": "RELATES_TO",
                                "properties": {
                                    "data_source": "jira"
                                }
                            })
    
    return relationships

def enrich_relationships(relationships: List[Dict[str, Any]], entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Enrich relationship data with additional properties based on entity data.
    
    Args:
        relationships: List of relationship dictionaries
        entities: List of entity dictionaries
        
    Returns:
        Enriched list of relationship dictionaries
    """
    # Create a lookup dictionary for entities by id
    entity_lookup = {}
    for entity in entities:
        entity_id = entity.get("entity", {}).get("id")
        if entity_id:
            entity_lookup[entity_id] = entity
    
    enriched_relationships = []
    for rel in relationships:
        source_id = rel.get("source")
        target_id = rel.get("target")
        relationship_type = rel.get("relationship")
        
        if source_id in entity_lookup and target_id in entity_lookup:
            source_entity = entity_lookup[source_id]
            target_entity = entity_lookup[target_id]
            source_type = source_entity.get("entity", {}).get("type")
            target_type = target_entity.get("entity", {}).get("type")
            
            # Get valid relationship types between these entity types
            valid_relationships = get_relationship_types(source_type, target_type)
            
            # Find the relationship definition
            rel_def = None
            for r in valid_relationships:
                if r.get("name") == relationship_type:
                    rel_def = r
                    break
            
            if rel_def:
                # Copy the original relationship
                enriched_rel = rel.copy()
                
                # Add relationship description
                enriched_rel["description"] = rel_def.get("description", "")
                
                # Add source and target entity types
                enriched_rel["source_type"] = source_type
                enriched_rel["target_type"] = target_type
                
                # Add source and target entity names for readability
                source_name = source_entity.get("entity", {}).get("attributes", {}).get("Summary", source_id)
                if source_type == "Person":
                    source_name = source_id  # For Person entities, the ID is the name
                
                target_name = target_entity.get("entity", {}).get("attributes", {}).get("Summary", target_id)
                if target_type == "Person":
                    target_name = target_id  # For Person entities, the ID is the name
                
                enriched_rel["source_name"] = source_name
                enriched_rel["target_name"] = target_name
                
                # Enrich properties based on entity data
                properties = enriched_rel.get("properties", {})
                
                if relationship_type == "ASSIGNED_TO":
                    # Add assignment date from issue timeline
                    timeline = target_entity.get("categories", {}).get("Timeline", {})
                    if isinstance(timeline, dict) and "Updated" in timeline:
                        properties["date_assigned"] = timeline["Updated"]
                    elif isinstance(timeline, str) and "Updated:" in timeline:
                        updated_part = timeline.split("Updated:")[1].split(",")[0].strip()
                        properties["date_assigned"] = updated_part
                
                elif relationship_type == "REPORTED":
                    # Add report date from issue timeline
                    timeline = target_entity.get("categories", {}).get("Timeline", {})
                    if isinstance(timeline, dict) and "Created" in timeline:
                        properties["date_reported"] = timeline["Created"]
                    elif isinstance(timeline, str) and "Created:" in timeline:
                        created_part = timeline.split("Created:")[1].split(",")[0].strip()
                        properties["date_reported"] = created_part
                    
                    # Add priority information
                    priority = target_entity.get("categories", {}).get("Priority")
                    if priority:
                        properties["priority_set"] = priority
                
                elif relationship_type == "CONTAINS":
                    # Add issue type and priority
                    issue_type = target_entity.get("entity", {}).get("attributes", {}).get("Issue Type")
                    if issue_type:
                        properties["issue_type"] = issue_type
                    
                    priority = target_entity.get("categories", {}).get("Priority")
                    if priority:
                        properties["priority_in_project"] = priority
                
                enriched_rel["properties"] = properties
                enriched_relationships.append(enriched_rel)
            else:
                # If no valid relationship definition found, just add the original
                enriched_relationships.append(rel)
        else:
            # If source or target entity not found, just add the original
            enriched_relationships.append(rel)
    
    return enriched_relationships

def main():
    """Main function to run the script."""
    parser = argparse.ArgumentParser(description="Extract relationships from Jira categorized data")
    parser.add_argument("--input", required=True, help="Path to the input JSON file")
    parser.add_argument("--output", required=True, help="Path to the output JSON file")
    
    args = parser.parse_args()
    
    # Load input data
    try:
        with open(args.input, 'r') as f:
            data = json.load(f)
        logger.info(f"Loaded input data from {args.input}")
    except Exception as e:
        logger.error(f"Failed to load input data: {e}")
        sys.exit(1)
    
    # Extract entities
    entities = data.get("entities", [])
    data_source = data.get("data_source", "jira")
    
    # Extract explicit relationships
    explicit_relationships = extract_explicit_relationships(entities, data_source)
    logger.info(f"Extracted {len(explicit_relationships)} explicit relationships")
    
    # Extract implicit relationships
    implicit_relationships = extract_implicit_relationships(entities)
    logger.info(f"Extracted {len(implicit_relationships)} implicit relationships")
    
    # Combine relationships
    all_relationships = explicit_relationships + implicit_relationships
    
    # Enrich relationships
    enriched_relationships = enrich_relationships(all_relationships, entities)
    logger.info(f"Enriched {len(enriched_relationships)} relationships")
    
    # Create output data
    output_data = {
        "status": "success",
        "data_source": data_source,
        "relationships": enriched_relationships
    }
    
    # Save output data
    try:
        with open(args.output, 'w') as f:
            json.dump(output_data, f, indent=2)
        logger.info(f"Saved relationships to {args.output}")
    except Exception as e:
        logger.error(f"Failed to save output data: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
