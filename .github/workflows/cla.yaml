name: "CLA Assistant"
on:
  issue_comment:
    types: [created]
  pull_request_target:
    types: [opened,closed,synchronize]

permissions:
  actions: write
  contents: write
  pull-requests: write
  statuses: write

jobs:
  CLAAssistant:
    runs-on: ubuntu-latest
    steps:
      - name: "CLA Assistant"
        if: > 
          github.event_name == 'pull_request_target' ||
          (github.event.comment && (
            github.event.comment.body == 'recheck' ||
            github.event.comment.body == 'I have read the CLA Document and I hereby sign the CLA'
          ))
        uses: contributor-assistant/github-action@v2.6.1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          PERSONAL_ACCESS_TOKEN: ${{ secrets.PERSONAL_ACCESS_TOKEN }}

        with:
          path-to-signatures: '.github/signatures/version1/cla.json'
          path-to-document: 'https://github.com/existence-master/Sentient/blob/master/CLA.md'
          branch: 'oss'  
          allowlist: itsskofficial,<PERSON><PERSON>r2004,dependabot[bot]
          remote-organization-name: existence-master
          remote-repository-name: Sentient-Internal