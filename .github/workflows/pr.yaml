name: PR Checks

on:
  pull_request:
    branches:
      - master
      - development

jobs:
  pre-check:
    name: PR Base Branch Check
    runs-on: ubuntu-latest
    steps:
      - name: Check if PR targets master and is not from allowed users
        run: |
          AUTHOR="${{ github.actor }}"
          BASE_BRANCH="${{ github.base_ref }}"
          ALLOWED_USERS=("itsskofficial" "Kabeer2004")

          if [[ "$BASE_BRANCH" == "master" && ! " ${ALLOWED_USERS[@]} " =~ " $AUTHOR " ]]; then
            echo "PRs to master are only allowed by: ${ALLOWED_USERS[*]}"
            exit 1
          fi
        shell: bash

  security-checks:
    name: Security & Secrets Scan
    runs-on: ubuntu-latest
    needs: pre-check
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run GitLeaks (Secret Scanning)
        uses: gitleaks/gitleaks-action@v2
        with:
          config-path: ../gitleaks.toml
          fail: true

      - name: Run Dependabot Security Audit (JS/TS)
        working-directory: src/interface
        run: npm audit --audit-level=high || true

  linting:
    name: Code Linting & Formatting
    runs-on: ubuntu-latest
    needs: pre-check
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Install JS/TS dependencies
        working-directory: src/interface
        run: npm ci

      - name: Run ESLint (JavaScript/TypeScript)
        working-directory: src/interface
        run: npm run lint

      - name: Run Prettier Check
        working-directory: src/interface
        run: npm run format:check

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.11"

      - name: Install Python dependencies
        working-directory: src/model
        run: |
          python -m pip install --upgrade pip
          pip install black flake8

      - name: Run Ruff (Python Linting & Formatting)
        working-directory: src/model
        run: ruff check . && ruff format --check .