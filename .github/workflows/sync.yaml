name: Sync Dynamic Branches

on:
  push:
    branches:
      - master
      - development
      - "feature/*"
      - "bugfix/*" 
      - "research/*"

jobs:
  sync:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout source repository
        uses: actions/checkout@v4

      - name: Identify Source and Target Branches
        id: set-branches
        run: |
          SOURCE_BRANCH=${GITHUB_REF#refs/heads/}  # Extract branch name
          
          if [[ "$SOURCE_BRANCH" == "master" ]]; then
            TARGET_BRANCH="development"
          elif [[ "$SOURCE_BRANCH" == "development" ]]; then
            TARGET_BRANCH="development#stage/combined"
          else
            TARGET_BRANCH="${SOURCE_BRANCH}"  # Fallback branch
          fi

          echo "SOURCE_BRANCH=$SOURCE_BRANCH" >> $GITHUB_ENV
          echo "TARGET_BRANCH=$TARGET_BRANCH" >> $GITHUB_ENV

      - name: Push changes to private repository branch
        env:
          GITHUB_TOKEN: ${{ secrets.PERSONAL_ACCESS_TOKEN }}
          ORG_NAME: existence-master
          ORIGIN<PERSON>_REPO_NAME: Sentient
          DESTINATION_REPO_NAME: Sentient-Internal
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "itsskofficial"

          # Clone the destination repository
          git clone --branch ${{ env.TARGET_BRANCH }} https://${{ env.GITHUB_TOKEN }}@github.com/${{ env.ORG_NAME }}/${{ env.DESTINATION_REPO_NAME }}.git

          # Sync files (exclude .git, destination repo, and handle vanished files)
          rsync -av --exclude='.git' --exclude="${{ env.DESTINATION_REPO_NAME}}/" --delete --ignore-missing-args $GITHUB_WORKSPACE/ ${{ env.DESTINATION_REPO_NAME}}/ || echo "Ignoring rsync exit code 24"

          cd ${{ env.DESTINATION_REPO_NAME }}

          # Commit and push changes
          git add .
          git commit -m "Synced from ${{ env.SOURCE_BRANCH }} to ${{ env.TARGET_BRANCH }} on $(date)" || echo "No changes to commit"
          git push origin ${{ env.TARGET_BRANCH }}
