---
name: "🐛 Bug Report"
about: "Report an issue to improve the project."
title: "[BUG] "
labels: ["bug"]
assignees: []

---

### 📝 Description
A clear and concise description of the issue.

### 🔍 Steps to Reproduce
1. Go to '...'
2. Click on '...'
3. See the error '...'

### ✅ Expected Behavior
What should happen?

### ❌ Actual Behavior
What actually happens?

### 🖥️ Environment
- OS: [e.g., Windows 10, macOS Ventura]
- Browser: [e.g., Chrome 114, Firefox 102]
- Node.js/Python Version: [if applicable]
- App Version: [e.g., v1.2.3]

### 📸 Screenshots (if applicable)
Upload screenshots or logs.

### 🏗 Additional Context
Add any other relevant context.
