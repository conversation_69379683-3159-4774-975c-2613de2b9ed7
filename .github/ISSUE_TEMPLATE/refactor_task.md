---
name: "♻️ Refactor Task"
about: "Refactor existing code to improve maintainability, performance, or readability."
title: "[REFACTOR] "
labels: ["refactor"]
assignees: []

---

### 🔍 Description
Briefly describe what needs to be refactored and why.

### 🎯 Goals
- Improve code readability/maintainability/performance.
- Reduce code duplication.
- Improve adherence to best practices.

### 📌 Areas to Refactor
- [ ] Function/module: `<module_name>/<function_name>`
- [ ] File(s): `<file_path>`
- [ ] Logic: `<describe the logic change>`

### 💡 Suggested Approach
Provide an outline of how the refactor should be done.

### 📚 References
- Related issues: #issue_number (if applicable)
- Docs/Resources: [link]

### ✅ Definition of Done
- [ ] Code is cleaner and more modular.
- [ ] Tests are updated and pass.
- [ ] No breaking changes introduced.

### 🔄 Additional Context
Any other relevant information.