{"status": "success", "data_source": "jira", "entities": [{"entity": {"type": "JiraIssue", "id": "10001", "attributes": {"Issue Key": "PROJ-101", "Summary": "Implement OAuth2 integration", "Description": "Enable OAuth2 login support for the internal tools using Okta.", "Issue Type": "Task", "Status": "In Progress", "Priority": "High", "Assignee": "<PERSON>", "Reporter": "<PERSON>", "Project": "Internal Tools Upgrade (PROJ)", "Created": "2025-05-01T09:30:00.000+0000", "Updated": "2025-05-16T14:22:00.000+0000", "Due Date": "2025-06-01", "Labels": ["authentication", "security"]}}, "categories": {"Issue Summary": "Implement OAuth2 integration", "Description": "Enable OAuth2 login support for the internal tools using Okta.", "Status": "In Progress", "Priority": "High", "Assignee": "<PERSON> (<EMAIL>)", "Reporter": "<PERSON> (<EMAIL>)", "Comments": "Comment 1 by <PERSON> on 2025-05-02T10:00:00.000+0000: Please make sure to test SSO before merging. Comment 2 by <PERSON> on 2025-05-10T15:43:00.000+0000: Okta integration is working in staging now.", "Attachments": "", "Related Issues": "", "Timeline": "Created: 2025-05-01T09:30:00.000+0000, Updated: 2025-05-16T14:22:00.000+0000, Due Date: 2025-06-01", "Miscellaneous": "Issue Key: PROJ-101, Issue Type: Task, Project: Internal Tools Upgrade (PROJ), Labels: authentication, security"}}, {"entity": {"type": "Person", "id": "<PERSON>", "attributes": {"role": "Assignee", "email": "<EMAIL>"}}, "categories": {"Personal Information": "", "Work/Professional": "Assignee: <PERSON> (<EMAIL>). Project: Internal Tools Upgrade (PROJ). Task: Implement OAuth2 integration. Status: In Progress. Comment 2 by <PERSON> on 2025-05-10T15:43:00.000+0000: Okta integration is working in staging now.", "Education": "", "Interests/Hobbies": "", "Projects": "Project: Internal Tools Upgrade (PROJ). Task: Implement OAuth2 integration. Status: In Progress. Comment 2 by <PERSON> on 2025-05-10T15:43:00.000+0000: Okta integration is working in staging now.", "Events/Calendar": "Created: 2025-05-01T09:30:00.000+0000. Updated: 2025-05-16T14:22:00.000+0000. Due Date: 2025-06-01.", "Contacts/Relationships": "Reporter: <PERSON> (<EMAIL>)", "Miscellaneous": "Labels: authentication, security"}}, {"entity": {"type": "Person", "id": "<PERSON>", "attributes": {"role": "Reporter", "email": "<EMAIL>"}}, "categories": {"Personal Information": "", "Work/Professional": "Reporter: <PERSON> (<EMAIL>)", "Education": "", "Interests/Hobbies": "", "Projects": "Project: Internal Tools Upgrade (PROJ)", "Events/Calendar": "Created: 2025-05-01T09:30:00.000+0000, Updated: 2025-05-16T14:22:00.000+0000, Due Date: 2025-06-01", "Contacts/Relationships": "Assignee: <PERSON> (<EMAIL>)", "Miscellaneous": "Comment 1 by <PERSON> on 2025-05-02T10:00:00.000+0000: Please make sure to test SSO before merging."}}, {"entity": {"type": "Project", "id": "Internal Tools Upgrade (PROJ)", "attributes": {}}, "categories": {"Project Overview": "", "Objectives": "Implement OAuth2 integration, Conduct security audit for new release", "Timeline": "Issue PROJ-101 Due Date: 2025-06-01, Issue PROJ-102 Due Date: 2025-06-10", "Team Members": "Assignee for Issue PROJ-101: <PERSON> (<EMAIL>), Reporter for Issue PROJ-101: <PERSON> (<EMAIL>), Reporter for Issue PROJ-102: <PERSON> (<EMAIL>)", "Resources": "", "Status": "Issue PROJ-101 Status: In Progress, Issue PROJ-102 Status: To Do", "Deliverables": "Enable OAuth2 login support for the internal tools using Okta, Audit the platform for OWASP top 10 vulnerabilities", "Risks/Issues": "Issue ID: 10001, Issue Key: PROJ-101, Issue ID: 10002, Issue Key: PROJ-102", "Miscellaneous": "Comments on Issue PROJ-101: Comment 1 by <PERSON> on 2025-05-02T10:00:00.000+0000: Please make sure to test SSO before merging. Comment 2 by <PERSON> on 2025-05-10T15:43:00.000+0000: Okta integration is working in staging now."}}, {"entity": {"type": "JiraIssue", "id": "10002", "attributes": {"Issue Key": "PROJ-102", "Summary": "Conduct security audit for new release", "Description": "Audit the platform for OWASP top 10 vulnerabilities.", "Issue Type": "Story", "Status": "To Do", "Priority": "Medium", "Assignee": "Unassigned", "Reporter": "<PERSON>", "Project": "Internal Tools Upgrade (PROJ)", "Created": "2025-05-05T08:00:00.000+0000", "Updated": "2025-05-06T12:30:00.000+0000", "Due Date": "2025-06-10", "Labels": ["security", "audit"]}}, "categories": {"Issue Summary": "Conduct security audit for new release", "Description": "Audit the platform for OWASP top 10 vulnerabilities.", "Status": "To Do", "Priority": "Medium", "Assignee": "Unassigned", "Reporter": "<PERSON> (<EMAIL>)", "Comments": "", "Attachments": "", "Related Issues": "", "Timeline": {"Created": "2025-05-05T08:00:00.000+0000", "Updated": "2025-05-06T12:30:00.000+0000", "Due Date": "2025-06-10"}, "Miscellaneous": {"Issue Type": "Story", "Project": "Internal Tools Upgrade (PROJ)", "Labels": "security, audit"}}}, {"entity": {"type": "Person", "id": "<PERSON>", "attributes": {"role": "Reporter", "email": "<EMAIL>"}}, "categories": {"Personal Information": "", "Work/Professional": "Reporter: <PERSON> (<EMAIL>), Project: Internal Tools Upgrade (PROJ)", "Education": "", "Interests/Hobbies": "", "Projects": "Issue ID: 10002, Issue Key: PROJ-102, Summary: Conduct security audit for new release, Description: Audit the platform for OWASP top 10 vulnerabilities, Issue Type: Story, Status: To Do, Priority: Medium, Assignee: Unassigned, Created: 2025-05-05T08:00:00.000+0000, Updated: 2025-05-06T12:30:00.000+0000, Due Date: 2025-06-10, Labels: security, audit", "Events/Calendar": "Created: 2025-05-05T08:00:00.000+0000, Updated: 2025-05-06T12:30:00.000+0000, Due Date: 2025-06-10", "Contacts/Relationships": "", "Miscellaneous": ""}}]}