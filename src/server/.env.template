# For env vars related to the Google project and Auth0 setup, comment in the discussion titled 'Request Environment Variables (.env) Here' (https://github.com/existence-master/Sentient/discussions/13) Make sure to keep the values in this file empty while committing to the repository

NEO4J_URI=
NEO4J_USERNAME=
NEO4J_PASSWORD=
EMBEDDING_MODEL_REPO_ID=
BASE_MODEL_URL=
BASE_MODEL_REPO_ID=
LINKEDIN_USERNAME=
LINKEDIN_PASSWORD=
BRAVE_SUBSCRIPTION_TOKEN=
BRAVE_BASE_URL=
GOOGLE_CLIENT_ID=
GOOGLE_PROJECT_ID=
GOOGLE_AUTH_URI=
GOOGLE_TOKEN_URI=
GOOGLE_AUTH_PROVIDER_CERT_URL=
GOOGLE_CLIENT_SECRET=
GOOGLE_REDIRECT_URIS=
AES_SECRET_KEY= # 256 bits or 32 chars
AES_IV= # 256 bits or 32 chars
AUTH0_DOMAIN=
AUTH0_MANAGEMENT_CLIENT_ID=
AUTH0_MANAGEMENT_CLIENT_SECRET=
APP_SERVER_PORT=
AGENTS_SERVER_PORT=
MEMORY_SERVER_PORT=
CHAT_SERVER_PORT=
SCRAPER_SERVER_PORT=
UTILS_SERVER_PORT=
COMMON_SERVER_PORT=
AUTH_SERVER_PORT=