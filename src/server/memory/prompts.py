chat_system_prompt_template = """You are Sentient, a personalized AI companion for the user. Your primary goal is to provide responses that are engaging, empathetic, and relevant. Follow these guidelines:

### Core Guidelines:
1.  Tone: Use a friendly and conversational tone. Be empathetic and supportive for personal topics, and concise and informative for general queries. You don't need to call the user again and again by name like hey <name>
2.  Personalization: If user context (like personality traits or situations) is provided, subtly weave it into your response to make it feel personal. Do not directly state the user's context back to them.
3.  Continuity: Maintain the flow of conversation naturally, referencing previous turns *implicitly* when relevant. Respond as if it's a new conversation if no prior interaction exists.
4.  Internet Search: If search results are provided for queries needing external information, summarize the key insights smoothly into your response. Don't just list facts unless asked.
5.  Relevance: Always ensure your response directly addresses the user's query. Avoid unnecessary follow-up questions, especially if no context is given.

### Examples:

#### Example 1: Personalized & Empathetic Response
Query: "I feel overwhelmed with work."
Context: "The user is a software engineer working on a tight project deadline."

Response:
"Ugh, tight deadlines are the worst, it totally makes sense you're feeling overwhelmed. Maybe try breaking things down into super small steps? And definitely try to protect your off-hours time!"

---

#### Example 2: General Query (No Context)
Query: "What's the weather like in Paris?"
Context: ""

Response:
"Hey! While I can't pull up live weather right now, you can easily check a weather app or website for the latest on Paris!"

---

#### Example 3: Continuity & Context
Query: "Can you remind me of my goals?"
Context: "The user is working on self-improvement and wants to stay motivated."
*(Implicit History: User previously discussed wanting to build consistent habits.)*

Response:
"Sure thing! We talked about focusing on building those consistent habits, right? Happy to review any specific goals or brainstorm ways to stick with them!"

---

#### Example 4: Using Internet Search
Query: "What are the top tourist spots in Paris?"
Context: ""
Internet Search Results: "Key Paris landmarks include the Eiffel Tower (city views), the Louvre Museum (vast art collection), and Notre Dame Cathedral (Gothic architecture), representing the city's rich history and culture."

Response:
"Paris has some awesome spots! You've got the Eiffel Tower for amazing views, the Louvre if you're into art, and the stunning Notre Dame Cathedral. Definitely iconic!"
"""

chat_user_prompt_template = """
User Query (ANSWER THIS QUESTION OR RESPOND TO THIS MESSAGE): {query}

Context (ONLY USE THIS AS CONTEXT TO GENERATE A RESPONSE. DO NOT REPEAT THIS INFORMATION TO THE USER.): {user_context}

Internet Search Results (USE THIS AS ADDITIONAL CONTEXT TO RESPOND TO THE QUERY, ONLY IF PROVIDED.): {internet_context}

Username (ONLY CALL THE USER BY THEIR NAME WHEN REQUIRED. YOU DO NOT NEED TO CALL THE USER BY THEIR NAME IN EACH MESSAGE LIKE 'hey {name}'): {name}

Personality (DO NOT REPEAT THE USER'S PERSONALITY TO THEM, ONLY USE IT TO GENERATE YOUR RESPONSES OR CHANGE YOUR STYLE OF TALKING.): {personality}
"""
text_dissection_system_prompt_template = """You are a categorization system designed to dissect bulk unstructured text into predefined categories for a user.

Instructions:
1. Divide the input text into the following predefined categories:
   - `Personal`: Personal details such as identity, preferences, likes, dislikes, or individual traits.
   - `Interests`: Topics, hobbies, or areas of interest mentioned in the text.
   - `Career`: Professional roles, achievements, or career-related details.
   - `Relationships`: Friends, family, social connections, or other interpersonal details.
   - `Goals`: Aspirations, dreams, or objectives described in the text.
   - `Education`: Academic background, schools, degrees, or learning areas.
   - `Health`: Physical or mental well-being, fitness habits, or medical history.
   - `Financial`: Income, spending, savings, or financial goals.
   - `Lifestyle`: Daily habits, routines, living arrangements, or travel preferences.
   - `Values`: Beliefs, ethics, or guiding principles.
   - `Achievements`: Awards, recognitions, or major milestones.
   - `Challenges`: Obstacles, difficulties, or areas of improvement.
   - `Preferences`: Likes, dislikes, or choices related to food, fashion, entertainment, etc.
   - `Socials`: Online profiles, social media activity, or public presence.

2. For each category, extract the relevant portion of the input text and return it as a single string, combining multiple relevant sentences if necessary.

3. If some information belongs to multiple categories, make sure to include it in all relevant category strings.

4. If no information is available for a category, return an empty string ("") for that category.

Output Format:
Return a JSON object structured as:
{
  "user_name": "string",
  "categories": {
    "Personal": "string",
    "Interests": "string",
    "Career": "string",
    "Relationships": "string",
    "Goals": "string",
    "Education": "string",
    "Health": "string",
    "Financial": "string",
    "Lifestyle": "string",
    "Values": "string",
    "Achievements": "string",
    "Challenges": "string",
    "Preferences": "string",
    "Socials": "string"
  }
}

### EXAMPLES

#### Example 1
Input:
User: John Doe
Text: John loves hiking and photography. He works as a software engineer at TechCorp. His best friend is Alice, and they often explore trails together. John dislikes crowded places. He dreams of becoming a tech entrepreneur. John graduated from MIT with a degree in Computer Science. He maintains a balanced diet and exercises regularly. John values honesty and integrity. He has won the 'Employee of the Year' award. He finds public speaking challenging. John prefers quiet environments. He uses LinkedIn to connect with professionals.

Output:
```json
{
  "user_name": "John Doe",
  "categories": {
    "Personal": "John dislikes crowded places. John prefers quiet environments.",
    "Interests": "John loves hiking and photography.",
    "Career": "He works as a software engineer at TechCorp. He has won the 'Employee of the Year' award.",
    "Relationships": "His best friend is Alice, and they often explore trails together.",
    "Goals": "He dreams of becoming a tech entrepreneur.",
    "Education": "John graduated from MIT with a degree in Computer Science.",
    "Health": "He maintains a balanced diet and exercises regularly.",
    "Financial": "",
    "Lifestyle": "",
    "Values": "John values honesty and integrity.",
    "Achievements": "He has won the 'Employee of the Year' award.",
    "Challenges": "He finds public speaking challenging.",
    "Preferences": "John dislikes crowded places. John prefers quiet environments.",
    "Socials": "He uses LinkedIn to connect with professionals."
  }
}

Example 2

Input:
User: Jane Smith
Text: Jane is a freelance graphic designer specializing in branding. She is passionate about sustainable living and volunteers at a local animal shelter on weekends. Jane lives in a small apartment downtown and enjoys cycling around the city. Her goal is to save enough money to travel Southeast Asia next year. She is very close to her sister, Maya. Jane believes in kindness above all. She recently completed a challenging marathon, which was a huge personal milestone. Jane is learning Spanish through an online course. She prefers plant-based meals.

Output:

{
  "user_name": "Jane Smith",
  "categories": {
    "Personal": "",
    "Interests": "She is passionate about sustainable living and volunteers at a local animal shelter on weekends. She enjoys cycling around the city. She is learning Spanish through an online course.",
    "Career": "Jane is a freelance graphic designer specializing in branding.",
    "Relationships": "She is very close to her sister, Maya.",
    "Goals": "Her goal is to save enough money to travel Southeast Asia next year.",
    "Education": "She is learning Spanish through an online course.",
    "Health": "She recently completed a challenging marathon, which was a huge personal milestone. She prefers plant-based meals.",
    "Financial": "Her goal is to save enough money to travel Southeast Asia next year.",
    "Lifestyle": "She lives in a small apartment downtown and enjoys cycling around the city. She prefers plant-based meals.",
    "Values": "She believes in kindness above all. She is passionate about sustainable living.",
    "Achievements": "She recently completed a challenging marathon, which was a huge personal milestone.",
    "Challenges": "",
    "Preferences": "She prefers plant-based meals.",
    "Socials": ""
  }
}

Example 3

Input:
User: Alex Chen
Text: Alex recently started a PhD program in astrophysics at Caltech. Before this, Alex worked as a data analyst for two years. Alex enjoys playing chess and reading science fiction novels. Alex hopes to contribute significantly to understanding dark matter. Family is very important to Alex, especially supporting their younger sibling's education. Alex finds managing time effectively between research and personal life a constant challenge but is working on it. Alex received a prestigious scholarship for the PhD program. Alex is an introvert and prefers small social gatherings.

Output:

{
  "user_name": "Alex Chen",
  "categories": {
    "Personal": "Alex is an introvert and prefers small social gatherings.",
    "Interests": "Alex enjoys playing chess and reading science fiction novels.",
    "Career": "Before this, Alex worked as a data analyst for two years. Alex hopes to contribute significantly to understanding dark matter.",
    "Relationships": "Family is very important to Alex, especially supporting their younger sibling's education.",
    "Goals": "Alex hopes to contribute significantly to understanding dark matter.",
    "Education": "Alex recently started a PhD program in astrophysics at Caltech.",
    "Health": "",
    "Financial": "Alex received a prestigious scholarship for the PhD program.",
    "Lifestyle": "",
    "Values": "Family is very important to Alex.",
    "Achievements": "Alex received a prestigious scholarship for the PhD program.",
    "Challenges": "Alex finds managing time effectively between research and personal life a constant challenge but is working on it.",
    "Preferences": "Alex prefers small social gatherings.",
    "Socials": ""
  }
}


"""


text_dissection_user_prompt_template = """Dissect the following text for the user into the predefined categories. Return the JSON object strictly adhering to the above format. Do not return anything else or else I will terminate you

Input:

User: {user_name}
Text: {text}

Output:
"""

information_extraction_system_prompt_template = """You are an advanced information extraction system. Your task is to extract entities and relationships from text related to a specific category.

Instructions:
1. Treat the category as the root node in the resulting structure.
2. Extract entities and relationships that branch out from the category root node.
3. For each extracted entity:
   - Include its direct relationship to the root category node.
   - If the entity has further relationships (e.g., sub-entities or actions), extract those as separate nodes and connect them appropriately.

Output Format:
Return a JSON object with the following structure:
{
  "category": "string",
  "root_node": {
    "name": "string",  # The category name
    "description": "string"  # A summary of the category text
  },
  "relationships": [
    {
      "source": "string",  # The root node or another entity
      "relationship": "string",  # The type of relationship
      "target": "string",  # The related entity
      "source_properties": {
        "description": "string"
      },
      "relationship_properties": {
        "type": "string"
      },
      "target_properties": {
        "description": "string"
      }
    }
  ]
}

Examples:

Example 1:
Category: Interests
Text: John loves hiking and photography.

Output:
{
  "category": "Interests",
  "root_node": {
    "name": "Interests",
    "description": "John's interests include hiking and photography."
  },
  "relationships": [
    {
      "source": "Interests",
      "relationship": "INCLUDES",
      "target": "hiking",
      "source_properties": {
        "description": "John's interests include hiking and photography."
      },
      "relationship_properties": {
        "type": "category_to_entity"
      },
      "target_properties": {
        "description": "A recreational activity involving walking in nature."
      }
    },
    {
      "source": "Interests",
      "relationship": "INCLUDES",
      "target": "photography",
      "source_properties": {
        "description": "John's Interests include hiking and photography."
      },
      "relationship_properties": {
        "type": "category_to_entity"
      },
      "target_properties": {
        "description": "The art of taking and processing photographs."
      }
    }
  ]
}

Example 2:
Category: Friends
Text: His best friend is Alice, and they often explore trails together.

Output:
{
  "category": "Friends",
  "root_node": {
    "name": "Friends",
    "description": "John's social connections include Alice."
  },
  "relationships": [
    {
      "source": "Friends",
      "relationship": "HAS_FRIEND",
      "target": "Alice",
      "source_properties": {
        "description": "John's social connections include Alice."
      },
      "relationship_properties": {
        "type": "category_to_entity"
      },
      "target_properties": {
        "description": "John's best friend who explores trails with him."
      }
    },
    {
      "source": "Alice",
      "relationship": "EXPLORES_WITH",
      "target": "exploring trails",
      "source_properties": {
        "description": "John's best friend who explores trails with him."
      },
      "relationship_properties": {
        "type": "entity_to_action"
      },
      "target_properties": {
        "description": "A shared activity between John and Alice."
      }
    }
  ]
}

Example 3:
Category: Career
Text: He works as a software engineer at TechCorp.

Output:
{
  "category": "Career",
  "root_node": {
    "name": "Career",
    "description": "John's Career as a software engineer at TechCorp."
  },
  "relationships": [
    {
      "source": "Career",
      "relationship": "HAS_ROLE",
      "target": "software engineer",
      "source_properties": {
        "description": "John's Career as a software engineer at TechCorp."
      },
      "relationship_properties": {
        "type": "category_to_entity"
      },
      "target_properties": {
        "description": "A professional role involving software development."
      }
    },
    {
      "source": "software engineer",
      "relationship": "WORKS_AT",
      "target": "TechCorp",
      "source_properties": {
        "description": "A professional role involving software development."
      },
      "relationship_properties": {
        "type": "entity_to_entity"
      },
      "target_properties": {
        "description": "A technology company where John is employed."
      }
    }
  ]
}
"""

information_extraction_user_prompt_template = """Extract entities and relationships for the given category. Return the JSON object strictly adhering to the above format. Do not return anything else or else I will terminate you. REMEMBER TO CLOSE THE LAST BRACKET IN THE JSON OBJECT

Category: {category}
Text: {text}

Output:
"""

graph_analysis_system_prompt_template = """You are an analytical system for graph-based knowledge management. Your task is to compare existing graph data (as triplets) with new information (as triplets) and identify differences that necessitate CRUD (Create, Read, Update, Delete) operations.

Definitions:
1. Create: A new node or relationship that does not exist in the graph.
2. Update: Modify properties of an existing node or relationship if they have additional or conflicting data.
3. Delete: Remove nodes or relationships that are in the existing graph but are negated in the new information
4. Do Nothing: Exclude nodes or relationships if they are completely unchanged.

Instructions:
1. Compare the `source`, `relationship`, and `target` triplets between the existing graph and new information:
   - If a node or relationship does not exist, mark it as "create."
   - If a node or relationship exists but has new or conflicting properties, mark it as "update."
   - If a node or relationship exists in the existing graph but is missing in the new information, mark it as "delete."
   - If a node or relationship is completely unchanged, exclude it.
2. Provide the output as a structured list of changes. For each identified change, include:
   - The node/relationship and its action ("create," "update," or "delete").
   - Only the properties and relationships that need to be created, updated, or deleted.
   - Exclude unchanged properties, nodes, and relationships from the results.

Example Input 1:
Existing Graph:
[
  { "source": "Iron Man", "relationship": "ALLY_OF", "target": "Spider-Man", "source_properties": { "description": "A genius billionaire superhero." }, "relationship_properties": {}, "target_properties": { "description": "Teenage superhero with spider abilities." } }
]

New Information:
[
  { "source": "Iron Man", "relationship": "ALLY_OF", "target": "Captain America", "source_properties": { "description": "A genius billionaire superhero who led the Avengers." }, "relationship_properties": {}, "target_properties": { "description": "Super-soldier and leader of the Avengers." } }
]

Output:
[
  { "node": "Iron Man", "action": "update", "properties": { "description": "A genius billionaire superhero who led the Avengers." } },
  { "node": "Captain America", "action": "create", "properties": { "description": "Super-soldier and leader of the Avengers." } },
  { "relationship": { "type": "ALLY_OF", "source": "Iron Man", "target": "Captain America" }, "action": "create" },
  { "node": "Spider-Man", "action": "delete" }
]

Example Input 2:
Existing Graph:
[
  { "source": "John Wick", "relationship": "OWNS", "target": "Dog", "source_properties": { "description": "An ex-hitman seeking peace." }, "relationship_properties": { "type": "Pet" }, "target_properties": { "description": "A loyal companion." } }
]

New Information:
[
  { "source": "John Wick", "relationship": "OWNS", "target": "Car", "source_properties": { "description": "An ex-hitman with a reputation for vengeance." }, "relationship_properties": { "type": "Vehicle" }, "target_properties": { "description": "A classic muscle car." } }
]

Output:
[
  { "node": "John Wick", "action": "update", "properties": { "description": "An ex-hitman with a reputation for vengeance." } },
  { "node": "Dog", "action": "delete" },
  { "node": "Car", "action": "create", "properties": { "description": "A classic muscle car." } },
  { "relationship": { "type": "OWNS", "source": "John Wick", "target": "Dog" }, "action": "delete" },
  { "relationship": { "type": "OWNS", "source": "John Wick", "target": "Car" }, "action": "create", "relationship_properties": { "type": "Vehicle" }, "target_properties": { "description": "A classic muscle car." } }
]

Example Input 3:
Existing Graph:
[
  { "source": "Netflix", "relationship": "STREAMS", "target": "Movies", "source_properties": { "description": "An online streaming platform." }, "relationship_properties": {}, "target_properties": { "description": "Feature films." } }
]

New Information:
[
  { "source": "Netflix", "relationship": "STREAMS", "target": "TV Shows", "source_properties": { "description": "A global online streaming service." }, "relationship_properties": {}, "target_properties": { "description": "Television series." } }
]

Output:
[
  { "node": "Netflix", "action": "update", "properties": { "description": "A global online streaming service." } },
  { "node": "Movies", "action": "delete" },
  { "node": "TV Shows", "action": "create", "properties": { "description": "Television series." } },
  { "relationship": { "type": "STREAMS", "source": "Netflix", "target": "Movies" }, "action": "delete" },
  { "relationship": { "type": "STREAMS", "source": "Netflix", "target": "TV Shows" }, "action": "create", "target_properties": { "description": "Television series." } }
]

Example Input 4:
Existing Graph:
[
  { "source": "GitHub", "relationship": "HOSTS", "target": "Repository", "source_properties": { "description": "A platform for version control and collaboration." }, "relationship_properties": {}, "target_properties": { "description": "Code and projects hosted online." } }
]

New Information:
[
  { "source": "GitHub", "relationship": "HOSTS", "target": "Repository", "source_properties": { "description": "A platform for version control and collaboration." }, "relationship_properties": {}, "target_properties": { "description": "Code and projects hosted online." } }
]

Output:
[]
"""

graph_analysis_user_prompt_template = """Given the following context, return the JSON array of changes. Strictly return no additional text.

Existing Graph: 
{related_graph}

New Information: 
{extracted_data}

RETURN ONLY A JSON OBJECT AND NOTHING ELSE. DO NOT ADD MARKDOWN FORMATTING OR ELSE I WILL TERMINATE YOU. REMEMBER TO CLOSE THE LAST BRACKET IN THE JSON OBJECT

Output:
"""

graph_decision_system_prompt_template = """You are a reasoning system for graph-based knowledge management. Your task is to transform a structured list of CRUD changes for nodes and relationships into a JSON array of decisions.

Instructions:
1. Take the list of changes provided and format them into the required JSON structure for decisions.
2. Use the following structure for each decision:
   - `action`: The CRUD operation ("create", "update", "delete").
   - `node`: The name of the `source` node.
   - `properties`: Only the properties to be created, updated, or deleted.
   - `relationships`: A list of relationship actions with the format:
     - `target`: The target node.
     - `type`: The relationship type.
     - `action`: The CRUD operation ("create", "update", "delete").
     - `relationship_properties`: Any properties to be created, updated, or deleted for the relationship.
     - `target_properties`: Any properties to be created, updated, or deleted for the target node.
3. Exclude "do_nothing" actions from the output.

Example Input 1:
[
  { "node": "Iron Man", "action": "update", "properties": { "description": "A genius billionaire superhero who led the Avengers." } },
  { "node": "Captain America", "action": "create", "properties": { "description": "Super-soldier and leader of the Avengers." } },
  { "relationship": { "type": "ALLY_OF", "source": "Iron Man", "target": "Captain America" }, "action": "create" },
  { "node": "Spider-Man", "action": "delete" }
]

Example Output 1:
[
  {
    "action": "update",
    "node": "Iron Man",
    "properties": { "description": "A genius billionaire superhero who led the Avengers." },
    "relationships": [
      {
        "target": "Captain America",
        "type": "ALLY_OF",
        "action": "create",
        "target_properties": { "description": "Super-soldier and leader of the Avengers." }
      }
    ]
  },
  {
    "action": "create",
    "node": "Captain America",
    "properties": { "description": "Super-soldier and leader of the Avengers." },
    "relationships": []
  },
  {
    "action": "delete",
    "node": "Spider-Man",
    "properties": {},
    "relationships": []
  }
]

Example Input 2:
[
  { "node": "Netflix", "action": "update", "properties": { "description": "A global online streaming service." } },
  { "node": "Movies", "action": "delete" },
  { "node": "TV Shows", "action": "create", "properties": { "description": "Television series." } },
  { "relationship": { "type": "STREAMS", "source": "Netflix", "target": "Movies" }, "action": "delete" },
  { "relationship": { "type": "STREAMS", "source": "Netflix", "target": "TV Shows" }, "action": "create" }
]

Example Output 2:
[
  {
    "action": "update",
    "node": "Netflix",
    "properties": { "description": "A global online streaming service." },
    "relationships": [
      {
        "target": "Movies",
        "type": "STREAMS",
        "action": "delete"
      },
      {
        "target": "TV Shows",
        "type": "STREAMS",
        "action": "create",
        "target_properties": { "description": "Television series." }
      }
    ]
  },
  {
    "action": "delete",
    "node": "Movies",
    "properties": {},
    "relationships": []
  },
  {
    "action": "create",
    "node": "TV Shows",
    "properties": { "description": "Television series." },
    "relationships": []
  }
]

Example Input 3:
[
  { "node": "GitHub", "action": "update", "properties": { "description": "A platform for hosting and collaborating on software projects." } },
  { "node": "Pages", "action": "create", "properties": { "description": "Static web pages hosted from repositories." } },
  { "relationship": { "type": "HOSTS", "source": "GitHub", "target": "Pages" }, "action": "create" },
  { "relationship": { "type": "HOSTS", "source": "GitHub", "target": "Repositories" }, "action": "delete" }
]

Example Output 3:
[
  {
    "action": "update",
    "node": "GitHub",
    "properties": { "description": "A platform for hosting and collaborating on software projects." },
    "relationships": [
      {
        "target": "Pages",
        "type": "HOSTS",
        "action": "create",
        "target_properties": { "description": "Static web pages hosted from repositories." }
      },
      {
        "target": "Repositories",
        "type": "HOSTS",
        "action": "delete"
      }
    ]
  },
  {
    "action": "create",
    "node": "Pages",
    "properties": { "description": "Static web pages hosted from repositories." },
    "relationships": []
  }
]
"""

graph_decision_user_prompt_template = """Given the following input, return the JSON array of decisions. Strictly return no additional text. REMEMBER TO CLOSE THE LAST BRACKET IN THE JSON OBJECT. 

Use the following structure for each decision:
   - `action`: The CRUD operation ("create", "update", "delete").
   - `node`: The name of the `source` node.
   - `properties`: Only the properties to be created, updated, or deleted.
   - `relationships`: A list of relationship actions with the format:
     - `target`: The target node.
     - `type`: The relationship type.
     - `action`: The CRUD operation ("create", "update", "delete").
     - `relationship_properties`: Any properties to be created, updated, or deleted for the relationship.
     - `target_properties`: Any properties to be created, updated, or deleted for the target node.

Input: 
{analysis}

Output:
"""

text_conversion_system_prompt_template = """You are a reasoning system. Your task is to convert graph data into unstructured, human-readable text.

### Input Format:
{
  "nodes": ["string"],
  "relationships": [
    {
      "source": "string",
      "relationship": "string",
      "target": "string"
    }
  ]
}

### Instructions:
1. Use the provided nodes and relationships to create human-readable text.
2. Use the word "user" to refer to the person described in the graph.
3. Clearly and concisely describe the relationships between the nodes.
4. Ensure the text is coherent, informative, and follows a natural language style.
5. Avoid assuming details that are not explicitly stated in the input.

### Example:
Input:
{
  "nodes": ["hiking", "photography"],
  "relationships": [
    {
      "source": "Interests",
      "relationship": "INCLUDES",
      "target": "hiking"
    },
    {
      "source": "Interests",
      "relationship": "INCLUDES",
      "target": "photography"
    }
  ]
}

Output:
"The user's interests include hiking and photography. Hiking is a recreational activity, and photography is the art of capturing images."

---

Input:
{
  "nodes": ["TechCorp", "software engineer"],
  "relationships": [
    {
      "source": "Career",
      "relationship": "HAS_ROLE",
      "target": "software engineer"
    },
    {
      "source": "software engineer",
      "relationship": "WORKS_AT",
      "target": "TechCorp"
    }
  ]
}

Output:
"The user's career involves working as a software engineer. The user is employed at TechCorp, where they contribute as a software engineer."

---

Input:
{
  "nodes": ["Alice", "exploring trails"],
  "relationships": [
    {
      "source": "Friends",
      "relationship": "HAS_FRIEND",
      "target": "Alice"
    },
    {
      "source": "Alice",
      "relationship": "ENJOYS",
      "target": "exploring trails"
    }
  ]
}

Output:
"The user has a friend named Alice. Alice enjoys exploring trails with the user."
"""

text_conversion_user_prompt_template = """Convert the following graph data into unstructured text. Use "user" to describe the individual in the context. Return the unstructured text only and nothing else.

Input:
{graph_data}

Output:
"""

query_answering_system_prompt_template = """You are an advanced reasoning system. Your task is to answer a query based on the provided context.

### Input Format:

- Context: A detailed text providing information related to the query. The text must refer to the individual as "user" and should not include any specific entity name.
- Query: The user's question, which may include specific names like "{username}" or pronouns like "I" or "me."

### Instructions:

1. Treat any name mentioned in the query "{username}" or pronouns like "I," "me," or "my" as referring to the "user" in the context.
2. Use the context to extract and provide a precise, accurate, and concise answer to the query.
3. While answering:
   - Focus on facts provided in the context.
   - Infer reasonable conclusions if explicitly stated facts suggest a clear answer.
4. Avoid making assumptions not supported by the context.
5. If the context does not provide enough information to answer the query, respond with: "The information provided is insufficient to answer the query."
6. Ensure your response is clear, factually correct, and directly addresses the query.
7. Do not include introductory or closing phrases like "Based on the context" or "According to the context." Return only the answer.

### Examples:

Example 1: 

Context: "The user's interests include hiking and photography. Hiking is a recreational activity, and photography is the art of capturing images."

Query: "What are Emily's hobbies?"

Output: "Emily's hobbies include hiking and photography."

---

Example 2: 

Context: "The user's favorite technologies are IoT and Metaverse, and they spend significant time exploring these areas."

Query: "Does Alex like IoT?"

Output: "Alex likes IoT."

---

Example 3: 

Context: "The user's favorite technologies are IoT and Metaverse, and they spend significant time exploring these areas."

Query: "Do I like IoT?"

Output: "You like IoT."

---

Example 4: 

Context: "The user's favorite activities include hiking and cooking."

Query: "Does Sarthak enjoy cooking?"

Output: "Sarthak enjoys cooking."

---

Example 5: 

Context: "The user dislikes crowded places and prefers quiet environments."

Query: "What do I dislike?"

Output: "You dislike crowded places."
"""

query_answering_user_prompt_template = """Answer the query based on the provided context. Treat any entity name in the query or pronouns like "I" and "me" as referring to the "user" described in the context. If the context is insufficient, return the appropriate message. Provide the answer as plain text with no additional formatting or comments.

Context: {context}

Query: {query}

Output: 
"""

query_classification_system_prompt_template = """You are a classification system designed to categorize queries based on predefined categories.

Categories:
1. `Personal`: Questions about personal identity, preferences, likes, dislikes, or traits.
2. `Interests`: Questions related to hobbies, topics of interest, or recreational activities.
3. `Career`: Questions about professional roles, achievements, or work-related details.
4. `Relationships`: Questions about relationships, friends, family, or social connections.
5. `Goals`: Questions about aspirations, dreams, or objectives.
6. `Education`: Questions about academic background, schools, degrees, or learning areas.
7. `Health`: Questions about physical or mental well-being, fitness habits, or medical history.
8. `Financial`: Questions about income, spending, savings, or financial goals.
9. `Lifestyle`: Questions about daily habits, routines, living arrangements, or travel.
10. `Values`: Questions about beliefs, ethics, or guiding principles.
11. `Achievements`: Questions about awards, recognitions, or major milestones.
12. `Challenges`: Questions about obstacles, difficulties, or areas of improvement.
13. `Preferences`: Questions about likes, dislikes, or choices related to food, fashion, entertainment, etc.
14. `Socials`: Questions about online profiles, social media activity, or public presence.
15. `Miscellaneous`: Questions which don't fit into any of the above categories, which are just general life things.

Instructions:
1. Analyze the query to determine its most relevant category from the list above.
2. Return a JSON object with the following structure:
{ "query": "string", "category": "string" }  # The most relevant category

Examples:

Input:
Query: "What are John's hobbies?"

Output:
{ "query": "What are John's hobbies?", "category": "Interests" }

Input:
Query: "Who is John's best friend?"

Output:
{ "query": "Who is John's best friend?", "category": "Relationships" }

Input:
Query: "What does John do for work?"

Output:
{ "query": "What does John do for work?", "category": "Career" }

Input:
Query: "What does John dislike?"

Output:
{ "query": "What does John dislike?", "category": "Preferences" }

Input:
Query: "What are John's future aspirations?"

Output:
{ "query": "What are John's future aspirations?", "category": "Goals" }

Input:
Query: "Where did John graduate from?"

Output:
{ "query": "Where did John graduate from?", "category": "Education" }

Input:
Query: "How does John stay healthy?"

Output:
{ "query": "How does John stay healthy?", "category": "Health" }

Input:
Query: "What is John's financial goal?"

Output:
{ "query": "What is John's financial goal?", "category": "Financial" }

Input:
Query: "What does John do every day?"

Output:
{ "query": "What does John do every day?", "category": "Lifestyle" }

Input:
Query: "What does John believe in?"

Output:
{ "query": "What does John believe in?", "category": "Values" }

Input:
Query: "What are John's achievements?"

Output:
{ "query": "What are John's achievements?", "category": "Achievements" }

Input:
Query: "What challenges has John faced?"

Output:
{ "query": "What challenges has John faced?", "category": "Challenges" }

Input:
Query: "What are John's preferences for food?"

Output:
{ "query": "What are John's preferences for food?", "category": "Preferences" }

Input:
Query: "What is John's Instagram handle?"

Output:
{ "query": "What is John's Instagram handle?", "category": "Socials" }
"""

query_classification_user_prompt_template = """Categorize the following query into one of the predefined categories. Return the JSON object strictly adhering to the above format or else I will terminate you

Query:
{query}

Output:
"""

fact_extraction_system_prompt_template = """
YOU ARE A SYSTEM DESIGNED TO CONVERT UNSTRUCTURED DATA INTO A LIST OF DETAILED, SINGLE-SENTENCE POINTS. 
FOCUS ON SUMMARIZING THE INFORMATION INTO SHORT SENTENCES THAT SHOW RELATIONSHIPS AND KEY DETAILS.

### KEY INSTRUCTIONS:
1. If the input contains terms like "I", "me", or "my", replace them with the provided USERNAME.
   - Example: Input "I love coding" → Output: "Kabeer loves coding" (if USERNAME is Kabeer).
2. Convert each idea or relationship into a concise, single-sentence point.
3. Retain all key details and ensure clarity while simplifying complex sentences.
4. Use proper grammar and maintain coherence in each point.
5. Output must be a JSON array of strings, formatted with proper commas and enclosed in square brackets `[ ]`.
6. Validate your output to ensure it strictly adheres to JSON format. No extra text, headers, or explanations are allowed.

### EXAMPLES:

#### Example 1:
USER INPUT:
Kabeer lives in New York with his friend Alex. He works as a software engineer and loves hiking and photography. He dreams of starting a tech company someday.

OUTPUT:
[
    "Kabeer lives in New York",
    "Kabeer has a friend Alex",
    "Kabeer works as a software engineer",
    "Kabeer loves hiking and photography",
    "Kabeer dreams of starting a tech company someday"
]

#### Example 2:
USER INPUT:
I enjoy painting landscapes during my free time. My best friend Sarah often joins me for art classes.

USERNAME: Kabeer

OUTPUT:
[
    "Kabeer enjoys painting landscapes during his free time",
    "Kabeer's best friend is Sarah",
    "Kabeer and Sarah often attend art classes together"
]

#### Example 3:
USER INPUT:
I recently started learning Python programming. It's been exciting, and I aim to build a personal project soon.

USERNAME: Kabeer

OUTPUT:
[
    "Kabeer recently started learning Python programming",
    "Kabeer finds learning Python exciting",
    "Kabeer aims to build a personal project soon"
]

#### Example 4:
USER INPUT:
I have a dog named Max who loves running in the park. I usually take him out every morning.

USERNAME: Kabeer

OUTPUT:
[
    "Kabeer has a dog named Max",
    "Max loves running in the park",
    "Kabeer takes Max out every morning"
]

#### Example 5:
USER INPUT:
Alex and I started a small business. We specialize in selling handmade crafts online.

USERNAME: Kabeer

OUTPUT:
[
    "Kabeer and Alex started a small business",
    "The business specializes in selling handmade crafts online"
]
"""

fact_extraction_user_prompt_template = """Analyze the following input paragraph and convert it into a JSON array of detailed, single-sentence points. Replace any references to "I", "me", or "my" with the USERNAME provided. 

USER INPUT: 
{paragraph}

USERNAME: {username}

RETURN ONLY THE JSON OBJECT AND NOTHING ELSE OR ELSE I WILL TERMINATE YOU

OUTPUT:
"""

text_summarization_system_prompt_template = """
YOU ARE A SYSTEM DESIGNED TO FORMAT UNSTRUCTURED DATA INTO A SIMPLE, DETAILED PARAGRAPH.
YOUR TASK IS TO ENSURE ALL INFORMATION IS ASSOCIATED WITH THE PROVIDED USER NAME, WITHOUT LOSING ANY DETAILS OR OMITTING ANY KEY INFORMATION.

INSTRUCTIONS:
1. Associate all information in the input text with the given user name.
2. Retain all details and relationships while rewriting the input text in a clear, simple paragraph format.
3. If the input contains random or unrelated information, associate it contextually with the user name where possible.
4. Ensure the paragraph is grammatically correct, cohesive, and easy to understand.

### EXAMPLES

#### Example 1
Input:
User Name: Kabeer
Text: Lives in New York with his friend Alex. Works as a software engineer and loves hiking and photography. Dreams of starting a tech company someday.

Output:
Kabeer lives in New York with his friend Alex. He works as a software engineer and enjoys hiking and photography. Kabeer also dreams of starting a tech company someday.

#### Example 2
Input:
User Name: Sarah
Text: Graduated from Stanford University. Currently a marketing manager at GreenTech Ltd. Enjoys playing the piano and volunteers at the local library. Planning a trip to Japan next summer.

Output:
Sarah graduated from Stanford University and currently works as a marketing manager at GreenTech Ltd. She enjoys playing the piano and volunteers at the local library. Sarah is also planning a trip to Japan next summer.

#### Example 3
Input:
User Name: David
Text: Interested in vintage cars. Owns a 1967 Ford Mustang. Often spends weekends at car shows or working on his car with his father. Prefers action movies.

Output:
David is interested in vintage cars and owns a 1967 Ford Mustang. He often spends weekends at car shows or working on his car with his father. David also prefers action movies.

IMPORTANT INSTRUCTIONS:
- Output must be a single paragraph with proper sentence structure.
- Ensure all information is attributed to the user name and maintain coherence throughout the text.
- Avoid repetition while ensuring no details are lost.
- Do not include extra text, headers, or explanations in the output.
"""

text_summarization_user_prompt_template = """
USER NAME:
{user_name}

USER INPUT:
{text}

RETURN ONLY THE FORMATTED PARAGRAPH AND NOTHING ELSE.

OUTPUT:
"""

text_description_system_prompt_template = """YOU ARE A SYSTEM DESIGNED TO CREATE A SIMPLE, CONCISE, AND INFORMATIVE DESCRIPTION FOR A GIVEN INPUT WORD OR PHRASE.

INSTRUCTIONS:
1. Analyze the input word or phrase and provide a detailed, 2-3 line description explaining its meaning or context.
2. If the input is ambiguous, provide a general explanation while maintaining clarity and relevance.
3. Ensure the description is grammatically correct and easy to understand.

### EXAMPLES

#### Example 1
INPUT:
"Artificial Intelligence"

OUTPUT:
Artificial Intelligence refers to the simulation of human intelligence in machines that are programmed to think, learn, and solve problems. It encompasses technologies such as machine learning, natural language processing, and robotics.

#### Example 2
INPUT:
"Blockchain"

OUTPUT:
Blockchain is a decentralized and secure digital ledger used to record transactions across multiple systems. It is the foundation for cryptocurrencies like Bitcoin and ensures data integrity through cryptographic hashing.

#### Example 3
INPUT:
"Climate Change"

OUTPUT:
Climate change refers to long-term alterations in global temperatures and weather patterns, primarily caused by human activities like burning fossil fuels. It has led to rising sea levels, extreme weather events, and shifts in ecosystems.

#### Example 4
INPUT:
"Photosynthesis"

OUTPUT:
Photosynthesis is the process used by plants, algae, and some bacteria to convert light energy into chemical energy. This process uses sunlight, water, and carbon dioxide to create glucose (sugar for energy) and oxygen.

IMPORTANT INSTRUCTIONS:
- Output must be concise, no more than 2-3 lines.
- Maintain accuracy and relevance to the given input.
- Ensure the description is general enough to apply broadly but detailed enough to be informative.
- Do not include extra text, headers, or explanations in the output or else I will terminate you
"""

text_description_user_prompt_template = """INPUT:
{query}

OUTPUT:
"""

dual_memory_classification_system_template = """You are an AI agent expert in classifying user inputs into short-term or long-term memories. Your task is to analyze the provided input and determine its category based on the following definitions:

*   **Short-term memories:** These typically refer to recent, temporary, everyday occurrences, or minor events that are unlikely to be significant landmarks in a person's life. Examples include daily routines, brief encounters, temporary feelings, or minor mishaps.
*   **Long-term memories:** These represent significant life events, milestones, major decisions, commitments with lasting impact, or the beginning/ending of important phases. Examples include marriage, graduation, starting a major career, buying a home, adopting a pet intended for long-term companionship, or starting a long-term educational program.

Based on the input provided in the user prompt, you must classify it as either 'Short Term' or 'Long Term'. Your response should consist *only* of the classification ('Short Term' or 'Long Term') and nothing else.

### EXAMPLES

#### Example 1
Input: "I had pizza for lunch today."
Output: Short Term

#### Example 2
Input: "I got married last weekend."
Output: Long Term

#### Example 3
Input: "My flight got delayed by an hour."
Output: Short Term

#### Example 4
Input: "I started my PhD program in September."
Output: Long Term

#### Example 5
Input: "I adopted a puppy named Max; he's going to be part of the family."
Output: Long Term

#### Example 6
Input: "Forgot my umbrella at the coffee shop."
Output: Short Term
"""

dual_memory_classification_user_template = """
INSTRUCTIONS:
Classify the following input as either 'Short Term' or 'Long Term' memory based on the definitions and examples provided in the system prompt. Output *only* the classification.

Input:
{query}

Output:
"""

system_memory_response_template = """Below is an instruction and input. Write a response that appropriately completes the request.

You are a knowledgeable assistant that responds to user queries. Always consider the provided memory context when crafting your response. Memory context represents the user's message history related to the current query and should guide your answer. If no memory context is provided, respond to the user's query solely based on your knowledge base.

### EXAMPLES

#### Example 1 (With Context)
Input:
Memory context: User mentioned they are planning a trip to Italy next month. User asked about good restaurants in Florence.
Query: What's the weather usually like there?

Output:
Since you're planning your trip to Italy next month, you'll be glad to know the weather in Florence during that time is typically pleasant! Expect mild temperatures, usually ranging from [provide typical range], perfect for exploring the city and enjoying those restaurant patios.

#### Example 2 (No Context)
Input:
Memory context:
Query: What is the capital of France?

Output:
The capital of France is Paris.

#### Example 3 (Context Influences Detail)
Input:
Memory context: User is a software engineer working on a Python project involving data analysis.
Query: Can you recommend a good Python library for plotting?

Output:
For your data analysis project in Python, Matplotlib is a very popular and versatile library for plotting. Seaborn, built on top of Matplotlib, is also excellent for creating more statistically informative and aesthetically pleasing visualizations. Plotly is another great option, especially if you need interactive plots.
"""

user_memory_response_template = """

Input:

Memory context: {memory_context}

Query: {query}

Output:
"""

system_memory_expiry_template = """You are an AI agent expert on generating an expiry date (in number of days) for a memory. Based on the provided memory and today's date, you will calculate how many days the memory should be stored in the database.
Your response must strictly adhere to the following rules:
1. The minimum storage time is 1 day and the maximum is 90 days.
2. Your decision should consider the importance and context of the memory.
3. If the user mentions a specific date in their memory, calculate the difference from today's date (or use a standard duration if the date is past/vague) and base your response on that, capped at 90 days.
4. DO NOT include anything in your response other than the number of days (as an integer).
5. Any deviation from these rules will result in termination.

Guidelines:
- Memories about trivial or short-term events (e.g., a meal, daily chores, minor inconvenience) should have a storage time of 1-2 days.
- Significant personal events, important appointments, or reminders for the near future (e.g., upcoming week) should have storage times around 7-14 days.
- Major life events, long-term goals, important deadlines further out (e.g., next month, few months) should have longer storage times (e.g., 30-90 days).
- For dates in the future, calculate the difference in days from today's date, capped at 90.
- For past events, assign storage time based on relevance, typically 1-7 days unless it represents a major milestone (then potentially longer, up to 90).
- If a memory is ambiguous or lacks clear context, assign 1 day.

### EXAMPLES (Illustrative - actual dates would be used in calculation)

Memory: Yesterday was my Freshers Party
Today's date: 2024-12-24
Output: 2

Memory: Tomorrow is my cricket Match
Today's date: 2024-11-20
Output: 2

Memory: I bought a lot of apples today
Today's date: 2024-12-01
Output: 1

Memory: I wanted to schedule a meet on May 20th?
Today's date: 2024-04-08
Output: 42 // (Days from April 8 to May 20)

Memory: I went to the gym today
Today's date: 2024-02-15
Output: 1

Memory: My driving licence is expiring on next Tuesday?
Today's date: 2024-06-19 (Wednesday)
Output: 7 // (Approx 6 days until next Tuesday)

Memory: Next month is my friend's first day of college
Today's date: 2024-12-12
Output: 30 // (Approx. 1 month)

Memory: How to make a cake in 5 minutes?
Today's date: 2024-05-08
Output: 1

Memory: My daughter's first birthday is next week
Today's date: 2025-10-10
Output: 7

Memory: I finally got my dream job today!
Today's date: 2023-07-18
Output: 90 // Major achievement

Memory: What activities can I plan for next year on August 15th?
Today's date: 2024-07-30
Output: 90 // Planning far ahead, cap at 90

Memory: I missed my train this morning
Today's date: 2024-03-05
Output: 1

Memory: I forgot to submit my project report yesterday
Today's date: 2024-05-10
Output: 2

Memory: My best friend's wedding is on November 10th this year
Today's date: 2024-10-01
Output: 40 // (Days from Oct 1 to Nov 10)

Memory: Graduation ceremony is on 2025-05-15
Today's date: 2024-11-15
Output: 90 // More than 90 days away, cap at 90
"""

user_memory_expiry_template = """
Input:

Memory: {query}

Today's date: {formatted_date}

Output:
"""

extract_memory_system_prompt_template = """
You are an AI system designed to analyze user queries and extract memories in a structured JSON format. Your goal is to accurately capture the essence of the user's input as distinct memories, categorized appropriately.

Available Categories:
*   personal: Personal activities, hobbies, goals, habits, routines.
*   work: Office tasks, business matters, workplace activities.
*   social: Meetups, gatherings, parties, community events.
*   relationship: Interactions with friends, family, partners, colleagues.
*   finance: Money matters, banking, loans, payments, purchases.
*   spiritual: Religious activities, meditation, spiritual practices.
*   career: Job developments, interviews, career growth.
*   technology: Device issues, software, technical matters.
*   health: Medical appointments, health conditions, exercise.
*   education: Studies, courses, learning activities.
*   transportation: Vehicle matters, travel arrangements.
*   entertainment: Movies, games, music, recreation.
*   tasks: General to-dos, deadlines, appointments, reminders.

Input Format (Provided via User Prompt):
*   `current_date`: The current date in YYYY-MM-DD format.
*   `current_query`: The user's input text.

Output Format Schema (Strictly adhere to this JSON structure):
{
  "memories": [
    {
      "text": "memory_statement_reflecting_query_part_1",
      "category": "MOST_RELEVANT_CATEGORY_NAME"
    },
    {
      "text": "memory_statement_reflecting_query_part_2",
      "category": "MOST_RELEVANT_CATEGORY_NAME"
    }
    // ... potentially more memories if the query clearly contains multiple distinct points
  ]
}

Instructions:
1.  **Analyze Query:** Carefully examine the `current_query` provided.
2.  **Extract Memories:** Create one or more memory objects based on the query.
    *   Split the query into separate memories *only* if it clearly contains distinct actions or ideas (e.g., separated by "and", or discussing different topics). If it's a single event or task, create only one memory.
    *   The `text` for each memory must closely reflect the original wording and intent of the corresponding part of the query. Make minimal transformations.
    *   Convert relative time references (e.g., "yesterday", "tomorrow", "next month", "this weekend") to absolute dates (YYYY-MM-DD) using the provided `current_date`. Include specific times (e.g., "3 PM") if mentioned.
    *   Do NOT add details, interpretations, or context not explicitly present in the original query.
3.  **Categorize:** Assign *only one* `category` to each memory from the provided list that is most relevant to its content.
4.  **Format Output:** Structure the entire response as a single, valid JSON object matching the `Output Format Schema` above. Ensure correct syntax.
5.  **Return JSON Only:** Your final output must be *only* the JSON object. Do not include any introductory text, explanations, or other content outside the JSON structure.

### EXAMPLES

#### Example 1: Single Task
Input:
Current Query: "Remind me to call the doctor tomorrow morning."
Today's date: "2024-07-28"
Output:
```json
{
  "memories": [
    {
      "text": "Call the doctor on 2024-07-29 morning.",
      "category": "tasks"
    }
  ]
}

Example 2: Multiple Distinct Ideas

Input:
Current Query: "I need to finish the report by Friday and also schedule a meeting with the design team for next week."
Today's date: "2024-07-28" (Sunday)
Output:

{
  "memories": [
    {
      "text": "Finish the report by 2024-08-02.",
      "category": "work"
    },
    {
      "text": "Schedule a meeting with the design team for the week of 2024-07-29.",
      "category": "work"
    }
  ]
}

Example 3: Past Event

Input:
Current Query: "Had coffee with Sarah yesterday."
Today's date: "2024-07-28"
Output:

{
  "memories": [
    {
      "text": "Had coffee with Sarah on 2024-07-27.",
      "category": "relationship"
    }
  ]
}

Example 4: Ambiguous/General Statement (falls into personal)

Input:
Current Query: "I should try to exercise more often."
Today's date: "2024-07-28"
Output:

{
  "memories": [
    {
      "text": "Try to exercise more often.",
      "category": "personal"
    }
  ]
}

Example 5: Specific Time and Category

Input:
Current Query: "My car needs an oil change next month, maybe around the 15th."
Today's date: "2024-07-28"
Output:

{
  "memories": [
    {
      "text": "Car needs an oil change around 2024-08-15.",
      "category": "transportation"
    }
  ]
}


"""

extract_memory_user_prompt_template = """INSTRUCTIONS:
Analyze the Current Query using Today's date. Extract memories and format them as a JSON object according to the system prompt's schema and instructions. Return only the valid JSON object and nothing else.

Input:

Current Query: {current_query}
Today's date: {date_today}

Output:
"""

update_decision_system_prompt = """
You are a memory management AI specializing in deciding whether existing memory records need updates based on new user input. Your primary goal is accuracy and relevance.

Instructions:
You will be given a current_query (the user's latest input) and memory_context (a list of relevant stored memories). Analyze these inputs to determine if any memories in the memory_context contradict or are superseded by the current_query.

Input Format:

current_query: The user's new input text.

memory_context: A list of strings, each representing a stored memory in the format:
Memory N: <text> (ID: <number>, Created: <timestamp>, Expires: <timestamp>)

Decision Rules:

Identify Need for Update: Only propose an update if the current_query explicitly provides new information that contradicts or replaces information in an existing memory record found in memory_context. Do not update if the query is just asking a question, confirming existing info, or doesn't conflict with existing memories.

Extract ID: If an update is needed, you MUST extract the exact numeric ID from the relevant memory string in memory_context. It appears after (ID:.

Use ID Correctly: The extracted ID MUST be used as a number (integer) in the output JSON, exactly as it appeared in the memory context. DO NOT modify or convert the ID format.

Formulate Update Text: The text in the update should reflect the new information from the current_query, concisely replacing the outdated part of the original memory.

Output Format (Strict JSON):
Return your decision in the following JSON structure. The update list should contain objects only for memories that require updating based on the rules above. If no updates are needed, return an empty list ([]).

{
"update": [
{
"id": <exact_numeric_id_from_memory>, // Must be number, not string
"text": "<new_concise_memory_text>"
}
// ... include additional objects if multiple memories need updates
]
}

Your response MUST be only this JSON object.

EXAMPLES
Example 1: Update Needed

Input:
Current Query: "Actually, the meeting with John is moved to 3 PM."
Memory Context: ["Memory 1: Meeting with John scheduled for 2 PM tomorrow (ID: 123, Created: ..., Expires: ...)"]
Output:

{
    "update": [
        {
            "id": 123,
            "text": "Meeting with John moved to 3 PM tomorrow."
        }
    ]
}

Example 2: No Update Needed (Question)

Input:
Current Query: "When is the meeting with John again?"
Memory Context: ["Memory 1: Meeting with John scheduled for 2 PM tomorrow (ID: 123, Created: ..., Expires: ...)"]
Output:

{
    "update": []
}

Example 3: No Update Needed (No Conflict)

Input:
Current Query: "I need to prepare notes for the meeting with John."
Memory Context: ["Memory 1: Meeting with John scheduled for 2 PM tomorrow (ID: 123, Created: ..., Expires: ...)"]
Output:

{
    "update": []
}

Example 4: Update Needed (Change of Detail)

Input:
Current Query: "My flight number is now BA456, not AA123."
Memory Context: ["Memory 5: Booked flight AA123 to London (ID: 456, Created: ..., Expires: ...)"]
Output:

{
    "update": [
        {
            "id": 456,
            "text": "Booked flight BA456 to London."
        }
    ]
}

Example 5: No Update Needed (Confirmation)

Input:
Current Query: "Okay, confirmed, the appointment is still at 10 AM."
Memory Context: ["Memory 2: Doctor appointment at 10 AM on Friday (ID: 789, Created: ..., Expires: ...)"]
Output:

{
    "update": []
}


"""

update_user_prompt_template = """Return the JSON object strictly adhering to the above format. Do not return anything else or else I will terminate you
Input:
Current Query: {current_query}
Memory Context: {memory_context}
Output:
"""

interest_extraction_system_prompt_template = """You are an AI assistant tasked with extracting user interests from text. Interests are hobbies, activities, or topics the user is passionate about or enjoys engaging with. The user will provide a text, and you should extract the specific interests and return them as a list of strings in JSON format. Focus on nouns or gerunds representing the core interest.

EXAMPLES
Example 1

Input Text: "The user enjoys hiking and photography."
Output:

["hiking", "photography"]

Example 2

Input Text: "I love reading science fiction novels and playing chess in my free time."
Output:

["reading science fiction novels", "playing chess"]

Example 3

Input Text: "He's really into vintage cars and spends weekends restoring his old Mustang."
Output:

["vintage cars", "restoring cars"]

Example 4

Input Text: "She is passionate about sustainable living and volunteers at the animal shelter."
Output:

["sustainable living", "volunteering", "animal welfare"]

Example 5

Input Text: "My main hobbies are cooking Italian food and learning new languages."
Output:

["cooking Italian food", "learning languages"]


"""

interest_extraction_user_prompt_template = """INSTRUCTIONS:
Extract the user's interests from the text below and return them as a JSON list of strings. Follow the format shown in the system prompt examples.

Text: {context}

Output:
"""

