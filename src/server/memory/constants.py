PERSONALITY_DESCRIPTIONS = {
    "E": "Extroverts are outgoing, enthusiastic, and gain energy from social interactions. They thrive in dynamic environments and enjoy networking and collaboration.",
    "I": "Introverts are reflective, thoughtful, and gain energy from solitude. They prefer deep connections over large gatherings and value introspection.",
    "S": "Sensors focus on facts, concrete details, and practical solutions. They excel in hands-on tasks and value reliability and precision in their work.",
    "N": "Intuitives are imaginative, future-oriented, and thrive in abstract thinking. They enjoy exploring concepts, patterns, and innovative ideas.",
    "T": "Thinkers prioritize logic, fairness, and objectivity in decision-making. They excel in analysis and are driven by principles and efficiency.",
    "F": "Feelers value empathy, harmony, and emotional connections. They are compassionate decision-makers who prioritize personal and interpersonal values.",
    "J": "Judgers prefer structure, planning, and order. They are goal-oriented, organized, and excel in environments with clear expectations.",
    "P": "Perceivers are adaptable, spontaneous, and open to new experiences. They enjoy flexibility and thrive in dynamic, unstructured settings.",
}

CATEGORIES = [
    {
        "name": "Personal",
        "description": "This category encompasses personal preferences, likes, dislikes, and traits.",
    },
    {
        "name": "Interests",
        "description": "This category includes information about hobbies, topics of interest, and recreational activities.",
    },
    {
        "name": "Career",
        "description": "This category holds information about professional roles, achievements, and work-related details.",
    },
    {
        "name": "Relationships",
        "description": "This category contains details about relationships, friendships, and social connections.",
    },
    {
        "name": "Goals",
        "description": "This category includes personal or professional objectives and targets.",
    },
    {
        "name": "Education",
        "description": "This category encompasses academic pursuits, degrees, and educational achievements.",
    },
    {
        "name": "Health",
        "description": "This category includes information related to physical and mental well-being.",
    },
    {
        "name": "Financial",
        "description": "This category holds details about financial status, income, investments, and expenses.",
    },
    {
        "name": "Lifestyle",
        "description": "This category includes habits, routines, and overall way of living.",
    },
    {
        "name": "Values",
        "description": "This category encompasses personal beliefs, principles, and ethics.",
    },
    {
        "name": "Achievements",
        "description": "This category includes significant accomplishments or milestones.",
    },
    {
        "name": "Challenges",
        "description": "This category encompasses obstacles, difficulties, or hardships faced.",
    },
    {
        "name": "Preferences",
        "description": "This category includes personal choices or inclinations.",
    },
    {
        "name": "Socials",
        "description": "This category includes social media usage, online presence, and interactions.",
    },
    {
        "name": "Miscellaneous",
        "description": "This category includes things which can't be categorized into other categories, mostly comprising of general information",
    },
]
