from wrapt_timeout_decorator import *  # Importing timeout decorator for functions from wrapt_timeout_decorator library
import json  # For working with JSON data
import asyncio  # For asynchronous programming
import numpy as np  # For numerical operations, especially for cosine similarity calculation
from sklearn.metrics.pairwise import (
    cosine_similarity,
)  # For calculating cosine similarity between vectors
from dotenv import load_dotenv

from .runnables import *  # Importing runnable classes or functions from runnables.py
from .functions import *  # Importing functions from functions.py
from .constants import *  # Importing constant variables from constants.py
from .prompts import *  # Importing prompt templates and related utilities from prompts.py
from server.app.helpers import *  # Importing helper functions from helpers.py

load_dotenv("server/.env")  # Load environment variables from .env file


async def generate_streaming_response(runnable, inputs: dict, stream: bool = False):
    """
    Generic function to generate a streaming or non-streaming response from any runnable.

    This function abstracts the process of invoking a runnable, handling both streaming
    and non-streaming responses based on the `stream` flag and the runnable's capabilities.

    Args:
        runnable: The runnable object (e.g., Langchain RunnableSequence) to invoke.
        inputs (dict): The input dictionary to pass to the runnable.
        stream (bool, optional): Whether to generate a streaming response. Defaults to False.
                                 If True, it attempts to use `runnable.stream_response` if available.

    Yields:
        str or dict or any: Tokens or the full response from the runnable. Yields based on streaming or non-streaming mode.
                             If an error occurs, it prints the error and yields None.
    """
    try:
        if stream and hasattr(runnable, "stream_response"):
            # If streaming is requested and the runnable supports stream_response, use it
            for token in await asyncio.to_thread(lambda: runnable.stream_response(inputs)):
                yield token
        else:
            # Otherwise, invoke the runnable normally for a full response
            response = runnable.invoke(inputs)
            yield response

    except Exception as e:
        print(f"An error occurred: {e}")
        yield None


def generate_response(
    runnable, message: str, user_context: str, internet_context: str, username: str
):
    """
    Generates a response from a runnable, incorporating user profile, chat history, and internet context.

    This function retrieves user personality from a local JSON database, combines it with provided
    contexts, and invokes the given runnable to generate a response.

    Args:
        runnable: The runnable object (e.g., Langchain RunnableSequence) to invoke.
        message (str): The user's input message.
        user_context (str): Contextual information about the user from chat history or other sources.
        internet_context (str): Contextual information retrieved from internet searches.
        username (str): The username of the current user.

    Returns:
        dict or None: The response generated by the runnable, or None if an error occurs.
    """
    try:
        with open("userProfileDb.json", "r", encoding="utf-8") as f:
            db = json.load(f)  # Load user profile database from JSON file

        personality_description = db["userData"].get(
            "personality", "None"
        )  # Get personality description from database, default to "None" if not found

        response = runnable.invoke(
            {
                "query": message,
                "user_context": user_context,
                "internet_context": internet_context,
                "name": username,
                "personality": personality_description,
            }
        )  # Invoke the runnable with combined context and user info

        return response
    except Exception as e:
        print(f"An error occurred in generating response: {e}")
        return None


def get_chat_history() -> Optional[List[Dict[str, str]]]:
    """
    Retrieve the chat history from the active chat for use with the Ollama backend.

    Reads the chat history from "chatsDb.json" and formats it into a list of dictionaries
    suitable for conversational models, indicating 'user' or 'assistant' role for each message.

    Returns:
        Optional[List[Dict[str, str]]]: Formatted chat history as a list of dictionaries, where each
                                        dictionary has 'role' ('user' or 'assistant') and 'content'
                                        (message text). Returns None if retrieval fails or no active chat exists.
    """
    try:
        with open("chatsDb.json", "r", encoding="utf-8") as f:
            db = json.load(f)  # Load chat database from JSON file

        active_chat_id = db.get("active_chat_id")
        if active_chat_id is None:
            return []  # No active chat, return empty history

        # Find the active chat
        active_chat = next((chat for chat in db["chats"] if chat["id"] == active_chat_id), None)
        if active_chat is None:
            return []  # Active chat not found, return empty history

        messages = active_chat.get("messages", [])  # Get messages from active chat

        formatted_chat_history: List[Dict[str, str]] = [
            {
                "role": "user" if entry["isUser"] else "assistant",
                "content": entry["message"],
            }
            for entry in messages  # Format all messages from active chat
        ]

        return formatted_chat_history

    except Exception as e:
        print(f"Error retrieving chat history: {str(e)}")
        return None  # Return None in case of error



def dissect_information_into_categories(
    user_name: str, bulk_text: str, text_dissection_runnable
):
    """
    Dissect bulk unstructured text into predefined categories using a text dissection runnable.

    This function takes a bulk of text and uses a provided runnable (likely a Langchain RunnableSequence)
    to categorize the information within the text based on predefined categories.

    Args:
        user_name (str): The username associated with the text (for context or personalization).
        bulk_text (str): The unstructured text to be dissected into categories.
        text_dissection_runnable: The runnable responsible for dissecting text into categories.

    Returns:
        dict: A dictionary where keys are category names and values are the text segments
              belonging to each category. Returns an empty dictionary if dissection fails.
    """
    try:
        categories = text_dissection_runnable.invoke(
            {"user_name": user_name, "text": bulk_text}
        )  # Invoke the text dissection runnable
        return categories
    except Exception as e:
        print(f"Error dissecting information into categories: {e}")
        return {}  # Return empty dictionary in case of error


def extract_entities_and_relationships_for_category(
    category_name: str, category_text: str, information_extraction_runnable
):
    """
    Extract entities and relationships for a specific category from the given text.

    Uses a provided runnable (likely a Langchain RunnableSequence) to identify entities and
    relationships within the text segment corresponding to a specific category.

    Args:
        category_name (str): The name of the category for which to extract information.
        category_text (str): The text segment belonging to the category.
        information_extraction_runnable: The runnable responsible for extracting entities and relationships.

    Returns:
        list[dict]: A list of dictionaries, where each dictionary represents a triplet (entity, relationship, entity)
                    extracted from the text. Returns an empty list if extraction fails.
    """
    try:
        triplets = information_extraction_runnable.invoke(
            {"category": category_name, "text": category_text}
        )  # Invoke the information extraction runnable
        return triplets
    except Exception as e:
        print(
            f"Error extracting entities and relationships for category {category_name}: {e}"
        )
        return []  # Return empty list in case of error


def create_predefined_graph(
    user_name: str, categories: list[dict], graph_driver, embed_model
):
    """
    Create a predefined graph structure with a root user node and category nodes.

    This function initializes a knowledge graph by creating a 'User' node as the root and connecting it
    to predefined 'Category' nodes. Each category node is created with a name and description,
    and embeddings for both title and description are generated and stored.

    Args:
        user_name (str): The name of the user for the root node.
        categories (list[dict]): A list of category dictionaries, each containing 'name' and 'description'.
        graph_driver: Neo4j graph driver instance for database interaction.
        embed_model: Embedding model instance for generating embeddings.

    Returns:
        str: "Failed to create graph." if an error occurs, otherwise implicitly returns None as the function completes successfully.
    """
    try:
        with graph_driver.session() as session:
            # Merge the User node, creating it if it doesn't exist, based on user name
            session.run(
                """
                MERGE (u:User {name: $name})
                """,
                name=user_name,
            )

            for category in categories:
                name = category["name"]  # Extract category name
                description = category["description"]  # Extract category description
                title_embedding = embed_model._embed([category["name"]])[
                    0
                ]  # Generate title embedding
                description_embedding = embed_model._embed([category["description"]])[
                    0
                ]  # Generate description embedding

                # Merge Category node, creating it if it doesn't exist, and set properties
                session.run(
                    """
                  MERGE (c:Category {name: $category})
                  SET c.description = $description,
                      c.title_embedding = $title_embedding,
                      c.description_embedding = $description_embedding,
                      c.category = $category
                  """,
                    category=name,
                    description=description,
                    title_embedding=title_embedding,
                    description_embedding=description_embedding,
                )

                # Create a HAS_CATEGORY relationship from User to Category
                session.run(
                    """
                    MATCH (u:User {name: $user_name})
                    MATCH (c:Category {name: $category})
                    MERGE (u)-[:HAS_CATEGORY]->(c)
                    """,
                    user_name=user_name,
                    category=name,
                )
    except Exception as e:
        print(f"Error creating predefined graph: {e}")
        return "Failed to create graph."  # Return error message if graph creation fails


def create_graph_with_categories(
    categories_and_triplets: dict,
    graph_driver,
    embed_model,
    user_name: str,
    categories: list[dict],
) -> str:
    """
    Create graphs for each category, treating each category as a distinct node connected to its sub-entities,
    and link all categories to the root user node. Add a 'category' property to each subnode.
    Each node will have two embeddings: title-embedding and description-embedding.

    This function takes extracted categories and triplets (entities and relationships) and populates the knowledge graph.
    It creates nodes for entities and categories, sets properties including embeddings, and establishes relationships
    between them. It also links all categories to the root 'User' node.

    Args:
        categories_and_triplets (dict): A dictionary containing categories as keys and extracted triplets as values.
                                        Structure: {category_name: {"triplets": [list of triplets]}}
        graph_driver: Neo4j graph driver instance for database interaction.
        embed_model: Embedding model instance for generating embeddings.
        user_name (str): The name of the user for linking categories to the root user node.
        categories (list[dict]): A list of category dictionaries (used for linking categories to user).

    Returns:
        str: "Graph successfully created!" if graph creation is successful,
             "Failed to create graph." if an error occurs during graph creation.
    """
    try:
        with graph_driver.session() as session:
            for (
                category,
                data,
            ) in (
                categories_and_triplets.items()
            ):  # Iterate through each category and its data
                for category_data in data[
                    "triplets"
                ]:  # Iterate through triplet data within each category
                    triplet_source = category_data[
                        "source"
                    ]  # Source of the triplet information
                    for triplet in category_data[
                        "relationships"
                    ]:  # Iterate through individual relationships (triplets)
                        if triplet["source"] == category:
                            # If the source of the relationship is the category itself
                            target = triplet["target"]  # Target entity name
                            target_props = triplet.get(
                                "target_properties", {}
                            )  # Properties of the target entity
                            rel_props = triplet.get(
                                "relationship_properties", {}
                            )  # Properties of the relationship
                            target_title_embedding = embed_model._embed([target])[
                                0
                            ]  # Generate title embedding for target entity
                            target_description_embedding = embed_model._embed(
                                [
                                    target_props.get("description", "")
                                ]  # Generate description embedding for target entity
                            )[0]

                            # Merge target entity node and set properties, including embeddings and category
                            session.run(
                                """
                                MERGE (t:Entity {name: $target})
                                SET t += $target_props,
                                    t.title_embedding = $target_title_embedding,
                                    t.description_embedding = $target_description_embedding,
                                    t.category = $category,
                                    t.source = $source
                                MERGE (c:Category {name: $category})
                                MERGE (c)-[r:`%s`]->(t)
                                SET r += $rel_props
                                """
                                % triplet[
                                    "relationship"
                                ],  # Relationship type is dynamically inserted
                                category=category,
                                target=target,
                                target_props=target_props,
                                target_title_embedding=target_title_embedding,
                                target_description_embedding=target_description_embedding,
                                rel_props=rel_props,
                                source=triplet_source,
                            )
                        else:
                            # If the source is another entity (not the category itself)
                            source = triplet["source"]  # Source entity name
                            target = triplet["target"]  # Target entity name
                            source_props = triplet.get(
                                "source_properties", {}
                            )  # Properties of the source entity
                            target_props = triplet.get(
                                "target_properties", {}
                            )  # Properties of the target entity
                            rel_props = triplet.get(
                                "relationship_properties", {}
                            )  # Properties of the relationship
                            source_title_embedding = embed_model._embed([source])[
                                0
                            ]  # Generate title embedding for source entity
                            source_description_embedding = embed_model._embed(
                                [
                                    source_props.get("description", "")
                                ]  # Generate description embedding for source entity
                            )[0]
                            target_title_embedding = embed_model._embed([target])[
                                0
                            ]  # Generate title embedding for target entity
                            target_description_embedding = embed_model._embed(
                                [
                                    target_props.get("description", "")
                                ]  # Generate description embedding for target entity
                            )[0]

                            # Merge source and target entity nodes and set properties, including embeddings and category
                            session.run(
                                """
                                MERGE (s:Entity {name: $source})
                                SET s += $source_props,
                                    s.title_embedding = $source_title_embedding,
                                    s.description_embedding = $source_description_embedding,
                                    s.category = $category,
                                    s.source = $triplet_source
                                MERGE (t:Entity {name: $target})
                                SET t += $target_props,
                                    t.title_embedding = $target_title_embedding,
                                    t.description_embedding = $target_description_embedding,
                                    t.category = $category,
                                    t.source = $triplet_source
                                MERGE (s)-[r:`%s`]->(t)
                                SET r += $rel_props
                                """
                                % triplet[
                                    "relationship"
                                ],  # Relationship type is dynamically inserted
                                source=source,
                                source_props=source_props,
                                source_title_embedding=source_title_embedding,
                                source_description_embedding=source_description_embedding,
                                target=target,
                                target_props=target_props,
                                target_title_embedding=target_title_embedding,
                                target_description_embedding=target_description_embedding,
                                rel_props=rel_props,
                                category=category,
                                triplet_source=triplet_source,
                            )

            for category in categories:
                # Link each category to the root User node with HAS_CATEGORY relationship
                session.run(
                    """
                    MATCH (u:User {name: $user_name})
                    MATCH (c:Category {name: $category})
                    MERGE (u)-[:HAS_CATEGORY]->(c)
                    """,
                    user_name=user_name,
                    category=category["name"],
                )

        return "Graph successfully created!"  # Return success message if graph creation is successful
    except Exception as e:
        print(f"Error creating graph with categories: {e}")
        return "Failed to create graph."  # Return error message if graph creation fails


def build_initial_knowledge_graph(
    user_name: str,
    extracted_texts: list[dict],
    graph_driver,
    embed_model,
    text_dissection_runnable,
    information_extraction_runnable,
) -> str:
    """
    Build the initial knowledge graph for a user using the predefined workflow.

    This function orchestrates the process of building a knowledge graph from unstructured texts.
    It involves creating predefined category nodes, dissecting input texts into categories,
    extracting entities and relationships within each category, and finally populating the graph
    with this information.

    Args:
        user_name (str): The name of the user for whom to build the knowledge graph.
        extracted_texts (list[dict]): A list of dictionaries, each containing 'text' and 'source' of the input information.
        graph_driver: Neo4j graph driver instance for database interaction.
        embed_model: Embedding model instance for generating embeddings.
        text_dissection_runnable: Runnable for dissecting text into categories.
        information_extraction_runnable: Runnable for extracting entities and relationships.

    Returns:
        str: Result message indicating success or failure of graph creation.
             Possible returns: "Graph successfully created!", "Failed to create graph.",
             "Failed to create graph: No categories generated.",
             "Failed to create graph: No entities or relationships extracted.",
             "Failed to create graph due to an error."
    """
    try:
        create_predefined_graph(
            user_name, CATEGORIES, graph_driver, embed_model
        )  # Create predefined category nodes
        
        print("Predefined graph created successfully.")

        categories = {
            "categories": {}
        }  # Initialize dictionary to store categorized texts
        for entry in extracted_texts:  # Process each extracted text entry
            text = entry["text"]  # Extract text content
            source = entry["source"]  # Extract text source

            if not text.strip():  # Skip empty texts
                continue

            dissected_data = dissect_information_into_categories(
                user_name, text, text_dissection_runnable
            )  # Dissect text into categories

            if (
                dissected_data
                and type(dissected_data) == dict
                and dissected_data.get("categories")
            ):
                for category, category_text in dissected_data[
                    "categories"
                ].items():  # Iterate through dissected categories
                    if category_text.strip():  # Skip empty category texts
                        if category not in categories["categories"]:
                            categories["categories"][
                                category
                            ] = []  # Initialize category list if not present
                        categories["categories"][category].append(
                            {"text": category_text, "source": source}
                        )  # Append text and source to category

        if not categories["categories"]:
            print("Error: Failed to dissect information into categories.")
            return "Failed to create graph: No categories generated."  # Return error if no categories were generated

        categories_and_triplets = {}  # Initialize dictionary to store categories and extracted triplets

        for category, texts_with_sources in categories[
            "categories"
        ].items():  # Process each category and its associated texts
            for (
                entry
            ) in texts_with_sources:  # Process each text entry within a category
                text = entry["text"]  # Extract text content
                source = entry["source"]  # Extract text source

                try:
                    triplets = extract_entities_and_relationships_for_category(
                        category, text, information_extraction_runnable
                    )  # Extract triplets

                    if triplets:
                        triplets["source"] = (
                            source  # Attach source information to triplets
                        )

                        if category not in categories_and_triplets:
                            categories_and_triplets[category] = {
                                "triplets": []
                            }  # Initialize triplet list for category if not present

                        categories_and_triplets[category]["triplets"].append(
                            triplets
                        )  # Append triplets to category data
                except Exception as e:
                    continue  # Continue to next text if triplet extraction fails for current text

        if not categories_and_triplets:
            print("Error: No entities or relationships extracted.")
            return "Failed to create graph: No entities or relationships extracted."  # Return error if no triplets were extracted

        graph_result = create_graph_with_categories(
            categories_and_triplets, graph_driver, embed_model, user_name, CATEGORIES
        )  # Create graph from categories and triplets
        
        print("Graph created successfully.")

        return graph_result  # Return the result of graph creation

    except Exception as e:
        print(f"Error in building knowledge graph: {e}")
        return "Failed to create graph due to an error."  # Return general error message if any exception occurs


def classify_query_into_category(
    query: str, query_classification_runnable
) -> str | None:
    """
    Classify the query into a predefined category using a query classification runnable.

    This function uses a provided runnable (likely a Langchain RunnableSequence) to classify
    a given query into one of the predefined categories.

    Args:
        query (str): The query string to be classified.
        query_classification_runnable: The runnable responsible for query classification.

    Returns:
        str or None: The name of the classified category if successful, otherwise None.
    """
    try:
        response = query_classification_runnable.invoke(
            {"query": query}
        )  # Invoke the query classification runnable
        return response["category"]  # Return the classified category from the response
    except Exception as e:
        print(f"Error classifying query into category: {e}")
        return None  # Return None in case of error


def perform_similarity_search_rag(
    category: str, query: str, graph_driver, embed_model
) -> dict:
    """
    Perform similarity search within nodes of a specific category for Retrieval-Augmented Generation (RAG).

    This function searches for nodes in the graph that belong to the specified category and are semantically
    similar to the given query. It uses cosine similarity on embeddings of node titles and descriptions
    to find relevant nodes. It also retrieves relationships connected to these similar nodes to provide context
    for RAG.

    Args:
        category (str): The category to search within.
        query (str): The query string to find similar nodes for.
        graph_driver: Neo4j graph driver instance for database interaction.
        embed_model: Embedding model instance for generating query embeddings and comparing node embeddings.

    Returns:
        dict: A dictionary containing 'nodes' and 'triplets'. 'nodes' is a list of similar nodes found,
              and 'triplets' is a list of relationship triplets connected to these nodes, providing context.
              Returns an empty dictionary with empty lists for nodes and triplets if search fails or no relevant nodes are found.
    """
    try:
        query_title_embedding = embed_model._embed([query])[
            0
        ]  # Generate title embedding for the query
        query_description_embedding = embed_model._embed([query])[
            0
        ]  # Generate description embedding for the query

        with graph_driver.session() as session:
            # Query to find entities within the specified category and retrieve their properties and embeddings
            query_result = session.run(
                """
                MATCH (e:Entity {category: $category})
                RETURN e.name AS name,
                       e.title_embedding AS title_embedding,
                       e.description_embedding AS description_embedding,
                       properties(e) AS properties
                """,
                category=category,
            )

            nodes = query_result.data()  # Fetch node data from query result

            similar_nodes = []  # Initialize list to store similar nodes
            related_triplets = []  # Initialize list to store related triplets

            for node in nodes:  # Iterate through each node fetched from the graph
                title_embedding = np.array(
                    node["title_embedding"]
                )  # Convert title embedding to numpy array
                description_embedding = np.array(
                    node["description_embedding"]
                )  # Convert description embedding to numpy array

                # Calculate cosine similarity between query and node title/description embeddings
                title_similarity = cosine_similarity(
                    [query_title_embedding], [title_embedding]
                )[0][0]
                description_similarity = cosine_similarity(
                    [query_description_embedding], [description_embedding]
                )[0][0]

                combined_similarity = (
                    title_similarity + description_similarity
                )  # Combine similarities

                if (
                    combined_similarity > 0.4
                ):  # Threshold for similarity to consider node as relevant
                    node_properties = {
                        k: v
                        for k, v in node["properties"].items()
                        if k
                        not in [
                            "title_embedding",
                            "description_embedding",
                        ]  # Exclude embedding properties from node properties
                    }
                    similar_nodes.append(
                        {"name": node["name"], "properties": node_properties}
                    )  # Add similar node to the list

            # Query to find relationships for the similar nodes (incoming and outgoing)
            relationships_query_result = session.run(
                """
                MATCH (n:Entity)-[r]->(related)
                WHERE n.name IN $node_names
                RETURN n.name AS source,
                       type(r) AS relationship,
                       related.name AS target,
                       properties(r) AS relationship_properties,
                       properties(related) AS target_properties
                UNION
                MATCH (related)-[r]->(n:Entity)
                WHERE n.name IN $node_names
                RETURN related.name AS source,
                       type(r) AS relationship,
                       n.name AS target,
                       properties(r) AS relationship_properties,
                       properties(n) AS target_properties
                """,
                node_names=[
                    node["name"] for node in similar_nodes
                ],  # Pass names of similar nodes to query
            ).data()

            for (
                record
            ) in relationships_query_result:  # Process each relationship record
                relationship_triplet = {
                    "source": record["source"],
                    "relationship": record["relationship"],
                    "target": record["target"],
                    "source_properties": {
                        k: v
                        for k, v in next(
                            (
                                node["properties"]
                                for node in similar_nodes
                                if node["name"] == record["source"]
                            ),
                            {},  # Default to empty dict if source node not found (shouldn't happen)
                        ).items()
                    },
                    "relationship_properties": record["relationship_properties"],
                    "target_properties": {
                        k: v
                        for k, v in record["target_properties"].items()
                        if k
                        not in [
                            "title_embedding",
                            "description_embedding",
                        ]  # Exclude embedding properties
                    },
                }
                related_triplets.append(
                    relationship_triplet
                )  # Append triplet to the list

        return {
            "nodes": similar_nodes,
            "triplets": related_triplets,
        }  # Return similar nodes and related triplets
    except Exception as e:
        print(f"Error performing similarity search: {e}")
        return {"nodes": [], "triplets": []}  # Return empty lists in case of error


def generate_unstructured_text_from_graph(
    graph_data: dict, text_conversion_runnable
) -> str:
    """
    Convert structured graph data into unstructured text using a text conversion runnable.

    This function takes graph data (nodes and triplets) and uses a provided runnable
    (likely a Langchain RunnableSequence) to convert this structured information into
    human-readable unstructured text, suitable for generating responses or summaries.

    Args:
        graph_data (dict): A dictionary containing 'nodes' and 'triplets' representing graph information.
                             Typically the output of `perform_similarity_search_rag`.
        text_conversion_runnable: The runnable responsible for converting graph data to unstructured text.

    Returns:
        str: The generated unstructured text summarizing the graph data.
             Returns an empty string if text generation fails.
    """
    try:
        response = text_conversion_runnable.invoke(
            {"graph_data": graph_data}
        )  # Invoke text conversion runnable
        return response  # Return the generated unstructured text
    except Exception as e:
        print(f"Error generating unstructured text: {e}")
        return ""  # Return empty string in case of error


def query_user_profile(
    query: str,
    graph_driver,
    embed_model,
    text_conversion_runnable,
    query_classification_runnable,
) -> str:
    """
    Process a query about the user's profile, retrieve relevant information from the knowledge graph,
    and return an answer in unstructured text format.

    This function orchestrates the process of answering user profile queries. It classifies the query into a category,
    performs a similarity search in the knowledge graph within that category, and then converts the retrieved graph data
    into unstructured text to form the answer.

    Args:
        query (str): The user's query about their profile.
        graph_driver: Neo4j graph driver instance for database interaction.
        embed_model: Embedding model instance for generating embeddings.
        text_conversion_runnable: Runnable for converting graph data to unstructured text.
        query_classification_runnable: Runnable for classifying user queries into categories.

    Returns:
        str: The answer to the user's query in unstructured text format.
             Possible returns include informative answers, "Unable to classify the query into a category.",
             "No relevant information found in any category.", "Unable to generate unstructured text from the graph.",
             or "Failed to process the query." in case of errors.
    """
    try:
        category = classify_query_into_category(
            query, query_classification_runnable
        )  # Classify the query into a category
        if not category:
            return None  # Return error if category classification fails

        graph_data = perform_similarity_search_rag(
            category, query, graph_driver, embed_model
        )  # Perform similarity search in the graph

        if len(graph_data.get("nodes")) == 0:
            # If no relevant nodes found in the classified category, try searching in "Miscellaneous" category
            graph_data = perform_similarity_search_rag(
                "Miscellaneous", query, graph_driver, embed_model
            )
            if len(graph_data.get("nodes")) == 0:
                return None  # Return error if no relevant info found even in Miscellaneous

        context = generate_unstructured_text_from_graph(
            graph_data, text_conversion_runnable
        )  # Generate unstructured text from graph data
        if not context:
            return None # Return error if text generation fails

        return context  # Return the generated context as the answer
    except Exception as e:
        print(f"Error processing user profile query: {e}")
        return None  # Return general error message if any exception occurs


def analyze_graph_differences(
    related_graph: list[dict], extracted_data: dict, graph_analysis_runnable
):
    """
    Analyze differences between the existing related graph and newly extracted data using a graph analysis runnable.

    This function compares the information present in the existing knowledge graph (related to a query) with
    newly extracted data. It uses a graph analysis runnable (likely a Langchain RunnableSequence) to determine
    the differences and identify potential CRUD (Create, Read, Update, Delete) operations needed to synchronize
    the graph with the new information.

    Args:
        related_graph (list[dict]): Graph data related to a query, typically from `perform_similarity_search_crud`.
                                    Expected to be in a format suitable for analysis.
        extracted_data (dict): Newly extracted data from text, intended to be compared with the related graph.
                               Expected to be in a format that the graph analysis runnable can process.
        graph_analysis_runnable: The runnable responsible for analyzing graph differences and suggesting CRUD operations.

    Returns:
        dict or None: Analysis results from the graph analysis runnable, typically suggesting CRUD operations.
                      Returns None if analysis fails.
    """
    differences = graph_analysis_runnable.invoke(
        {"related_graph": related_graph, "extracted_data": extracted_data}
    )  # Invoke graph analysis runnable

    try:
        return differences  # Return the analysis results
    except Exception as e:
        print(f"Error parsing graph differences: {e}")
        return None  # Return None in case of error


def perform_similarity_search_crud(
    category: str, query: str, graph_driver, embed_model
) -> list[dict]:
    """
    Perform similarity search within nodes of a specific category for CRUD (Create, Read, Update, Delete) operations.

    This function is similar to `perform_similarity_search_rag` but is tailored for CRUD operations. It searches for
    nodes in a given category that are semantically similar to a query and retrieves not just the nodes but also
    all incoming and outgoing relationships connected to these nodes. This comprehensive retrieval is necessary
    for accurately determining what parts of the graph need to be created, updated, or deleted.

    Args:
        category (str): The category to search within.
        query (str): The query string to find similar nodes for.
        graph_driver: Neo4j graph driver instance for database interaction.
        embed_model: Embedding model instance for generating query embeddings and comparing node embeddings.

    Returns:
        list[dict]: A list of relationship triplets associated with similar nodes. Each triplet represents a relationship
                     and includes source node, relationship type, target node, and their properties.
                     Returns a dictionary with an empty list for 'triplets' if search fails or no relevant nodes are found.
    """
    try:
        query_title_embedding = embed_model._embed([query])[
            0
        ]  # Generate title embedding for the query
        query_description_embedding = embed_model._embed([query])[
            0
        ]  # Generate description embedding for the query

        with graph_driver.session() as session:
            # Query to find entities within the specified category and retrieve their properties and embeddings
            query_result = session.run(
                """
                MATCH (e:Entity {category: $category})
                RETURN e.name AS name,
                       e.title_embedding AS title_embedding,
                       e.description_embedding AS description_embedding,
                       properties(e) AS properties
                """,
                category=category,
            )

            nodes = query_result.data()  # Fetch node data from query result

            related_triplets = []  # Initialize list to store related triplets

            for node in nodes:  # Iterate through each node fetched from the graph
                title_embedding = np.array(
                    node["title_embedding"]
                )  # Convert title embedding to numpy array
                description_embedding = np.array(
                    node["description_embedding"]
                )  # Convert description embedding to numpy array

                # Calculate cosine similarity between query and node title/description embeddings
                title_similarity = cosine_similarity(
                    [query_title_embedding], [title_embedding]
                )[0][0]
                description_similarity = cosine_similarity(
                    [query_description_embedding], [description_embedding]
                )[0][0]

                combined_similarity = (
                    title_similarity + description_similarity
                )  # Combine similarities

                if (
                    combined_similarity > 0.4
                ):  # Threshold for similarity to consider node as relevant
                    node_properties = {
                        k: v
                        for k, v in node["properties"].items()
                        if k
                        not in [
                            "title_embedding",
                            "description_embedding",
                        ]  # Exclude embedding properties
                    }

                    # Query to find all relationships (incoming and outgoing) for the current similar node
                    relationships_query_result = session.run(
                        """
                        MATCH (e:Entity)-[r]-(related)
                        WHERE e.name = $node_name
                        RETURN e.name AS source,
                               type(r) AS relationship,
                               related.name AS target,
                               properties(r) AS relationship_properties,
                               properties(related) AS target_properties
                        UNION
                        MATCH (related)-[r]-(e:Entity)
                        WHERE e.name = $node_name
                        RETURN related.name AS source,
                               type(r) AS relationship,
                               e.name AS target,
                               properties(r) AS relationship_properties,
                               properties(e) AS target_properties
                        """,
                        node_name=node["name"],  # Pass node name to query relationships
                    ).data()

                    for (
                        record
                    ) in relationships_query_result:  # Process each relationship record
                        triplet = {
                            "source": record["source"],
                            "relationship": record["relationship"],
                            "target": record["target"],
                            "source_properties": node_properties
                            if record["source"] == node["name"]
                            else {},  # Source node properties if source is the current node
                            "relationship_properties": record[
                                "relationship_properties"
                            ],
                            "target_properties": {
                                k: v
                                for k, v in record["target_properties"].items()
                                if k
                                not in [
                                    "title_embedding",
                                    "description_embedding",
                                ]  # Exclude embedding properties
                            },
                        }
                        related_triplets.append(triplet)  # Append triplet to the list

        return related_triplets  # Return list of related triplets
    except Exception as e:
        print(f"Error performing similarity search: {e}")
        return {
            "triplets": []
        }  # Return dictionary with empty triplets list in case of error


def execute_crud_operations(
    decisions: list[dict],
    graph_driver,
    embed_model,
    text_description_runnable,
    category: str,
) -> str:
    """
    Execute CRUD (Create, Read, Update, Delete) operations on the knowledge graph based on provided decisions.

    This function takes a list of decisions, each specifying a CRUD operation, and applies these operations to the
    knowledge graph using the Neo4j driver. It handles various edge cases such as duplicate nodes, disjoint nodes,
    and category-specific constraints. It also sanitizes properties and creates default descriptions for new nodes
    if necessary.

    Args:
        decisions (list[dict]): A list of decision dictionaries, each defining a CRUD operation.
                                 Each decision should specify 'action' (create, update, delete), 'node', and optionally
                                 'properties', and 'relationships'.
        graph_driver: Neo4j graph driver instance for database interaction.
        embed_model: Embedding model instance for generating embeddings for new nodes.
        text_description_runnable: Runnable for generating descriptions for new nodes if descriptions are missing.
        category (str): The category context for the CRUD operations, used for categorization of new nodes.

    Returns:
        str: "CRUD operations executed successfully." if all operations are executed without errors.
    """
    with (
        graph_driver.session() as session
    ):  # Open a Neo4j session for database operations

        def sanitize_properties(properties: dict) -> dict:
            """
            Sanitize properties to ensure they only contain valid types (str, int, float, bool, list of these types).
            Provides default values ("N/A") for missing or null properties and converts other types to string.

            Args:
                properties (dict): Dictionary of properties to sanitize.

            Returns:
                dict: Sanitized properties dictionary.
            """
            sanitized = {}
            for key, value in (
                properties or {}
            ).items():  # Iterate through provided properties or default to empty dict
                if isinstance(value, (str, int, float, bool)):
                    sanitized[key] = value  # Keep valid types as is
                elif isinstance(value, list) and all(
                    isinstance(i, (str, int, float, bool)) for i in value
                ):
                    sanitized[key] = value  # Keep lists of valid types as is
                else:
                    sanitized[key] = (
                        str(value) if value is not None else "N/A"
                    )  # Convert other types to string or use "N/A"
            return sanitized

        def is_category_node(node_name: str) -> bool:
            """
            Check if a node with the given name is a Category node in the graph.

            Args:
                node_name (str): Name of the node to check.

            Returns:
                bool: True if it's a Category node, False otherwise.
            """
            result = session.run(
                """
                MATCH (n:Category {name: $name})
                RETURN id(n) AS node_id
                """,
                name=node_name,
            ).single()
            return (
                result is not None
            )  # Return True if a Category node with the name exists

        def create_or_update_relationships(
            node_id: int, relationships: list[dict], category: str = None
        ):
            """
            Create or update relationships for an existing node in the graph.

            Handles both creating new relationships and updating properties of existing relationships.

            Args:
                node_id (int): ID of the source node for the relationships.
                relationships (list[dict]): List of relationship dictionaries, each specifying relationship type,
                                            target node name, relationship properties, and target node properties.
                category (str, optional): Category context, used for creating target nodes if they don't exist. Defaults to None.
            """
            for rel in (
                relationships or []
            ):  # Iterate through provided relationships or default to empty list
                relationship_type = rel.get(
                    "type", "RELATED_TO"
                )  # Get relationship type or default to "RELATED_TO"
                target = rel.get(
                    "target", "Unknown Target"
                )  # Get target node name or default to "Unknown Target"
                target_properties = sanitize_properties(
                    rel.get("target_properties", {})
                )  # Sanitize target node properties
                relationship_properties = sanitize_properties(
                    rel.get("relationship_properties", {})
                )  # Sanitize relationship properties

                target_id = create_node_and_relationships(
                    target, target_properties, [], category
                )  # Ensure target node exists or create it

                # Check if a relationship of the same type already exists between source and target nodes
                existing_relationship = session.run(
                    f"""
                    MATCH (a)-[r:`{relationship_type}`]->(b)
                    WHERE id(a) = $source_id AND id(b) = $target_id
                    RETURN r
                    """,
                    source_id=node_id,
                    target_id=target_id,
                ).single()

                if existing_relationship:
                    # If relationship exists, update its properties
                    session.run(
                        f"""
                        MATCH (a)-[r:`{relationship_type}`]->(b)
                        WHERE id(a) = $source_id AND id(b) = $target_id
                        SET r += $relationship_properties
                        """,
                        source_id=node_id,
                        target_id=target_id,
                        relationship_properties=relationship_properties,
                    )
                else:
                    # If relationship doesn't exist, create a new one
                    session.run(
                        f"""
                        MATCH (a) WHERE id(a) = $source_id
                        MATCH (b) WHERE id(b) = $target_id
                        MERGE (a)-[r:`{relationship_type}`]->(b)
                        SET r += $relationship_properties
                        """,
                        source_id=node_id,
                        target_id=target_id,
                        relationship_properties=relationship_properties,
                    )

        def create_node_and_relationships(
            node_name: str,
            properties: dict = None,
            relationships: list[dict] = None,
            category: str = None,
        ) -> int:
            """
            Create a node and its relationships in the graph. Handles category node constraints and prevents duplicate nodes.

            Args:
                node_name (str): Name of the node to create.
                properties (dict, optional): Properties of the node. Defaults to None.
                relationships (list[dict], optional): Relationships to create for the node. Defaults to None.
                category (str, optional): Category of the node. Defaults to None.

            Returns:
                int: ID of the created or existing node.
            """
            node_name = (
                node_name or "Unnamed Entity"
            )  # Default node name if not provided
            sanitized_properties = sanitize_properties(
                properties or {}
            )  # Sanitize node properties

            if is_category_node(node_name):
                # If node name is a category node, get its ID and update relationships
                category_node_id = session.run(
                    """
                    MATCH (n:Category {name: $name})
                    RETURN id(n) AS node_id
                    """,
                    name=node_name,
                ).single()["node_id"]
                create_or_update_relationships(
                    category_node_id, relationships, category
                )  # Update relationships for category node
                return category_node_id  # Return category node ID

            # Check if a node with the same name already exists
            result = session.run(
                """
                MATCH (n {name: $name})
                RETURN id(n) AS node_id
                """,
                name=node_name,
            ).single()

            if result:
                # If node exists, return its ID
                node_id = result["node_id"]
            else:
                # If node doesn't exist, create a new one
                title_embedding = embed_model._embed([node_name])[
                    0
                ]  # Generate title embedding for new node
                description = sanitized_properties.get(
                    "description", ""
                ) or text_description_runnable.invoke(
                    {"query": node_name}
                )  # Get description or generate one
                description_embedding = embed_model._embed([description])[
                    0
                ]  # Generate description embedding

                result = session.run(
                    """
                    CREATE (n:Entity {name: $name})
                    SET n += $sanitized_properties,
                        n.title_embedding = $title_embedding,
                        n.description_embedding = $description_embedding,
                        n.category = $category,
                        n.description = $description
                    RETURN id(n) AS node_id
                    """,
                    name=node_name,
                    sanitized_properties=sanitized_properties,
                    title_embedding=title_embedding,
                    description_embedding=description_embedding,
                    category=category
                    or "Miscellaneous",  # Use provided category or default to "Miscellaneous"
                    description=description,
                ).single()
                node_id = result["node_id"]  # Get ID of the newly created node

                if category:
                    # If a category is provided, create a HAS_ENTITY relationship from category to the new entity
                    session.run(
                        """
                        MATCH (c:Category {name: $category})
                        MATCH (n:Entity {name: $node_name})
                        MERGE (c)-[:HAS_ENTITY]->(n)
                        """,
                        category=category,
                        node_name=node_name,
                    )
                else:
                    # If no category provided, link to "Miscellaneous" category
                    session.run(
                        """
                        MATCH (c:Category {name: 'Miscellaneous'})
                        MATCH (n:Entity {name: $node_name})
                        MERGE (c)-[:HAS_ENTITY]->(n)
                        """,
                        node_name=node_name,
                    )

            create_or_update_relationships(
                node_id, relationships, category
            )  # Create/update relationships for the created node
            return node_id  # Return the ID of the created node

        def update_node_and_relationships(
            node_name: str,
            properties: dict = None,
            relationships: list[dict] = None,
            category: str = None,
        ):
            """
            Update a node and its relationships in the graph. Preserves existing properties and prevents duplicate nodes.

            Args:
                node_name (str): Name of the node to update.
                properties (dict, optional): Properties to update for the node. Defaults to None.
                relationships (list[dict], optional): Relationships to update for the node. Defaults to None.
                category (str, optional): Category context. Defaults to None.
            """
            if is_category_node(node_name):
                # If node name is a category node, just update relationships
                category_node_id = session.run(
                    """
                    MATCH (n:Category {name: $name})
                    RETURN id(n) AS node_id
                    """,
                    name=node_name,
                ).single()["node_id"]
                create_or_update_relationships(
                    category_node_id, relationships, category
                )  # Update relationships for category node
                return  # Exit function after updating category relationships

            sanitized_properties = sanitize_properties(
                properties or {}
            )  # Sanitize properties for update
            node_id = create_node_and_relationships(
                node_name, {}, [], category
            )  # Ensure node exists

            title_embedding = embed_model._embed([node_name])[
                0
            ]  # Generate title embedding for node
            description = sanitized_properties.get(
                "description", ""
            ) or text_description_runnable.invoke(
                input={"query": node_name}  # Generate description if missing
            )
            description_embedding = embed_model._embed([description])[
                0
            ]  # Generate description embedding

            # Update node properties, preserving existing embeddings if they exist
            session.run(
                """
                MATCH (n) WHERE id(n) = $node_id
                SET n += $sanitized_properties
                SET n.title_embedding = COALESCE(n.title_embedding, $title_embedding),
                    n.description_embedding = COALESCE(n.description_embedding, $description_embedding)
                """,
                node_id=node_id,
                sanitized_properties=sanitized_properties,
                title_embedding=title_embedding,
                description_embedding=description_embedding,
            )

            create_or_update_relationships(
                node_id, relationships, category
            )  # Update relationships for the node

        def delete_node_and_relationships(node_name: str):
            """
            Delete a node and all its relationships from the graph. Ensures no other nodes are unintentionally affected.

            Args:
                node_name (str): Name of the node to delete.
            """
            if is_category_node(node_name):
                return  # Prevent deletion of category nodes

            # Find the node to be deleted
            result = session.run(
                """
                MATCH (n {name: $name})
                RETURN id(n) AS node_id
                """,
                name=node_name,
            ).single()

            if not result:
                return  # If node not found, exit

            # Delete the node and all its relationships
            session.run(
                """
                MATCH (n {name: $name})
                DETACH DELETE n
                """,
                name=node_name,
            )

        # Process each decision from the decision list
        for decision in decisions:
            if decision["action"] == "delete":
                delete_node_and_relationships(
                    decision.get("node")
                )  # Execute delete operation

        for decision in decisions:
            if decision["action"] == "create":
                create_node_and_relationships(
                    decision.get("node"),
                    decision.get("properties", {}),
                    decision.get("relationships", []),
                    category=category,  # Pass category context for create operation
                )

        for decision in decisions:
            if decision["action"] == "update":
                update_node_and_relationships(
                    decision.get("node"),
                    decision.get("properties", {}),
                    decision.get("relationships", []),
                    category=category,  # Pass category context for update operation
                )

    return "CRUD operations executed successfully."  # Return success message after all operations are executed


def extract_entities_and_relationships(
    information: str, category: str, information_extraction_runnable
):
    """
    Extract entities and relationships from the provided information for a given category using a runnable.

    This function uses a provided runnable (likely a Langchain RunnableSequence) to extract structured information
    (entities and relationships) from a text snippet, within the context of a specified category.

    Args:
        information (str): The text information from which to extract entities and relationships.
        category (str): The category context for information extraction.
        information_extraction_runnable: The runnable responsible for extracting entities and relationships.

    Returns:
        dict: Extracted data in a structured format, typically a dictionary or a list of dictionaries.
              The exact structure depends on the output of the `information_extraction_runnable`.
    """
    extracted_data = information_extraction_runnable.invoke(
        {"category": category, "text": information}
    )  # Invoke information extraction runnable
    return extracted_data  # Return the extracted data


def determine_crud_operations(analysis: dict, graph_decision_runnable):
    """
    Determine CRUD (Create, Read, Update, Delete) operations based on the analysis results using a graph decision runnable.

    This function takes the analysis of graph differences (from `analyze_graph_differences`) and uses a graph decision
    runnable (likely a Langchain RunnableSequence) to decide on the necessary CRUD operations to reconcile the
    knowledge graph with new information.

    Args:
        analysis (dict): Analysis results from `analyze_graph_differences`, detailing differences between graph and new data.
        graph_decision_runnable: The runnable responsible for determining CRUD operations based on graph analysis.

    Returns:
        list[dict] or None: A list of decision dictionaries, each specifying a CRUD operation ('action', 'node', etc.).
                             Returns None if decision-making fails.
    """
    decisions = graph_decision_runnable.invoke(
        {"analysis": analysis}
    )  # Invoke graph decision runnable

    try:
        return decisions  # Return the decision list
    except Exception as e:
        print(f"Error parsing CRUD decisions: {e}")
        return None  # Return None in case of error


def crud_graph_operations(
    information: str,
    graph_driver,
    embed_model,
    query_classification_runnable,
    information_extraction_runnable,
    graph_analysis_runnable,
    graph_decision_runnable,
    text_description_runnable,
) -> str:
    """
    Perform CRUD (Create, Read, Update, Delete) graph operations based on the provided information, orchestrating
    through a series of runnables for classification, extraction, analysis, and decision-making.

    This function is the main orchestrator for performing CRUD operations on the knowledge graph. It takes unstructured
    information, classifies it into a category, extracts structured data, analyzes differences with the existing graph,
    determines necessary CRUD operations, and finally executes these operations on the graph database.

    Args:
        information (str): The unstructured text information to be used for CRUD operations.
        graph_driver: Neo4j graph driver instance for database interaction.
        embed_model: Embedding model instance for generating embeddings.
        query_classification_runnable: Runnable for classifying queries into categories.
        information_extraction_runnable: Runnable for extracting entities and relationships from text.
        graph_analysis_runnable: Runnable for analyzing differences between graph and new data.
        graph_decision_runnable: Runnable for determining CRUD operations based on graph analysis.
        text_description_runnable: Runnable for generating descriptions for new nodes if descriptions are missing.

    Returns:
        str: Result message indicating success or failure of CRUD operations.
             Possible returns: "CRUD operations executed successfully." or "Failed to perform CRUD operations." in case of errors.
    """
    try:
        category = classify_query_into_category(
            information, query_classification_runnable
        )  # Classify information into a category
        if not category:
            category = "Miscellaneous"  # Default to "Miscellaneous" category if classification fails

        extracted_data = extract_entities_and_relationships(
            information, category, information_extraction_runnable
        )  # Extract entities and relationships

        related_graph = perform_similarity_search_crud(
            category, information, graph_driver, embed_model
        )  # Search for related graph data for CRUD

        if not related_graph:
            # If no related graph found in classified category, search in "Miscellaneous"
            related_graph = perform_similarity_search_crud(
                "Miscellaneous", information, graph_driver, embed_model
            )

        analysis = analyze_graph_differences(
            related_graph, extracted_data, graph_analysis_runnable
        )  # Analyze differences between related graph and extracted data

        decisions = determine_crud_operations(
            analysis, graph_decision_runnable
        )  # Determine CRUD operations based on analysis

        result = execute_crud_operations(
            decisions, graph_driver, embed_model, text_description_runnable, category
        )  # Execute determined CRUD operations

        return result  # Return the result of CRUD operations
    except Exception as e:
        print(f"Error in CRUD operations: {e}")
        return "Failed to perform CRUD operations."  # Return error message if any exception occurs


def delete_source_subgraph(graph_driver, file_name: str):
    """
    Deletes nodes from the graph where the 'source' property matches the given file_name,
    along with all their relationships. This is used to remove information from a specific source.

    Args:
        graph_driver: Neo4j graph driver instance for database interaction.
        file_name (str): The file name or source identifier to match against the 'source' property of nodes.
    """
    try:
        with graph_driver.session() as session:  # Open a Neo4j session
            # Query to match and delete nodes with the specified source file name and detach all relationships
            session.run(
                """
                MATCH (n)
                WHERE n.source = $file_name
                DETACH DELETE n
                """,
                file_name=file_name,  # Parameter for file name to match against node's source property
            )
            print(
                f"Deleted subgraph related to source: {file_name}"
            )  # Print confirmation message

    except Exception as e:
        print(f"Error deleting subgraph: {e}")  # Print error message if deletion fails
