aiofiles==23.2.1
aiohappyeyeballs==2.4.6
aiohttp==3.11.12
aioice==0.9.0
aiortc==1.11.0
aiosignal==1.3.2
altgraph==0.17.4
annotated-types==0.7.0
anyio==4.8.0
attrs==25.1.0
audioread==3.0.1
av==14.2.0
beautifulsoup4==4.13.3
blis==1.2.0
cachetools==5.5.1
catalogue==2.0.10
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
cli_exit_tools==1.2.7
click==8.1.8
cloudpathlib==0.21.0
colorama==0.4.6
coloredlogs==15.0.1
confection==0.1.5
contourpy==1.3.1
cryptography==44.0.1
ctranslate2==4.5.0
cycler==0.12.1
cymem==2.0.11
dataclasses-json==0.6.7
decorator==5.2.1
Deprecated==1.2.18
dill==0.3.9
dirtyjson==1.0.8
diskcache==5.6.3
dnspython==2.7.0
einops==0.8.1
en_core_web_sm @ https://github.com/explosion/spacy-models/releases/download/en_core_web_sm-3.8.0/en_core_web_sm-3.8.0-py3-none-any.whl#sha256=1932429db727d4bff3deed6b34cfc05df17794f4a52eeb26cf8928f7c1a0fb85
fastapi==0.115.8
faster-whisper==1.1.1
fastrtc==0.0.18
ffmpy==0.5.0
filelock==3.17.0
filetype==1.2.0
flatbuffers==25.2.10
fonttools==4.56.0
frozenlist==1.5.0
fsspec==2025.2.0
func_timeout==4.3.5
google==3.0.0
google-api-core==2.24.1
google-api-python-client==2.161.0
google-auth==2.38.0
google-auth-httplib2==0.2.0
google-auth-oauthlib==1.2.1
google-crc32c==1.7.1
googleapis-common-protos==1.67.0
gradio==5.23.3
gradio_client==1.8.0
greenlet==3.1.1
groovy==0.1.2
h11==0.14.0
httpcore==1.0.7
httplib2==0.22.0
httpx==0.28.1
huggingface-hub==0.29.1
humanfriendly==10.0
idna==3.10
ifaddr==0.2.0
Jinja2==3.1.5
joblib==1.4.2
json_repair==0.39.0
kaleido==0.2.1
kiwisolver==1.4.8
langcodes==3.5.0
language_data==1.3.0
lazy_loader==0.4
lib-detect-testenv==2.0.8
librosa==0.11.0
linkedin-api==2.3.1
llama-index-core==0.12.19
llama-index-embeddings-huggingface==0.5.1
llama_cpp_python==0.3.8
llvmlite==0.44.0
lxml==5.3.1
marisa-trie==1.2.1
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.26.1
matplotlib==3.10.1
mdurl==0.1.2
mpmath==1.3.0
msgpack==1.1.0
multidict==6.1.0
multiprocess==0.70.17
murmurhash==1.0.12
mypy-extensions==1.0.0
neo4j==5.28.1
nest-asyncio==1.6.0
networkx==3.4.2
nltk==3.9.1
ntscraper==0.3.17
numba==0.61.0
numpy==1.26.4
oauthlib==3.2.2
onnxruntime==1.21.0
orjson==3.10.16
packaging==24.2
pandas==2.2.3
pefile==2023.2.7
pillow==11.1.0
platformdirs==4.3.7
pooch==1.8.2
praw==7.8.1
prawcore==2.4.0
preshed==3.0.9
propcache==0.2.1
proto-plus==1.26.0
protobuf==5.29.3
psutil==7.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.1
pycparser==2.22
pydantic==2.10.6
pydantic_core==2.27.2
pydub==0.25.1
pyee==13.0.0
Pygments==2.19.1
pyinstaller==6.12.0
pyinstaller-hooks-contrib==2025.1
pylibsrtp==0.11.0
pyngrok==7.2.3
pyOpenSSL==25.0.0
pyparsing==3.2.1
pyreadline3==3.5.4
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-multipart==0.0.20
pytz==2025.1
pywin32-ctypes==0.2.3
PyYAML==6.0.2
regex==2024.11.6
requests==2.32.3
requests-oauthlib==2.0.0
rich==13.9.4
rsa==4.9
ruff==0.9.6
safehttpx==0.1.6
safetensors==0.5.2
scikit-learn==1.6.1
scipy==1.15.2
semantic-version==2.10.0
sentence-transformers==3.4.1
shellingham==1.5.4
six==1.17.0
smart-open==7.1.0
snac==1.2.1
sniffio==1.3.1
soundfile==0.13.1
soupsieve==2.6
soxr==0.5.0.post1
spacy==3.8.4
spacy-legacy==3.0.12
spacy-loggers==1.0.5
SQLAlchemy==2.0.38
srsly==2.5.1
sse-starlette==2.2.1
starlette==0.45.3
sympy==1.13.1
tenacity==9.0.0
thinc==8.3.4
threadpoolctl==3.5.0
tiktoken==0.9.0
tokenizers==0.21.0
tomlkit==0.13.2
torch==2.6.0
tqdm==4.67.1
transformers==4.49.0
typer==0.15.2
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2025.1
tzlocal==5.3
update-checker==0.18.0
uritemplate==4.1.1
urllib3==2.3.0
uvicorn==0.34.0
wasabi==1.1.3
weasel==0.4.1
websocket-client==1.8.0
websockets==15.0.1
wrapt==1.17.2
wrapt_timeout_decorator==1.5.1
yarl==1.18.3
# pip install llama-cpp-python --extra-index-url https://abetlen.github.io/llama-cpp-python/whl/<cuda-version>