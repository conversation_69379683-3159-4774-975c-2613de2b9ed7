import os  # For interacting with the operating system, e.g., accessing environment variables
from wrapt_timeout_decorator import *  # Importing timeout decorator for functions from wrapt_timeout_decorator library
import json  # For working with JSON data
import requests  # For making HTTP requests
import asyncio  # For asynchronous programming
from dotenv import load_dotenv

from .prompts import *  # Importing prompt templates and related utilities from prompts.py
from server.app.helpers import *  # Importing helper functions from helpers.py

load_dotenv("server/.env")  # Load environment variables from .env file


async def generate_streaming_response(runnable, inputs: dict, stream: bool = False):
    """
    Generates a streaming or non-streaming response from a given runnable.

    This function abstracts the process of invoking a runnable, handling both streaming
    and non-streaming responses based on the `stream` flag and the runnable's capabilities.

    Args:
        runnable: The runnable object (e.g., Langchain RunnableSequence) to invoke.
        inputs (dict): The input dictionary to pass to the runnable.
        stream (bool, optional): Whether to generate a streaming response. Defaults to False.
                                 If True, it attempts to use `runnable.stream_response` if available.

    Yields:
        str or dict or any: Tokens or the full response from the runnable. Yields based on streaming or non-streaming mode.
                             If an error occurs, it prints the error and yields None.
    """
    try:
        if stream and hasattr(runnable, "stream_response"):
            # If streaming is requested and the runnable supports stream_response, use it
            for token in await asyncio.to_thread(lambda: runnable.stream_response(inputs)):
                yield token
        else:
            # Otherwise, invoke the runnable normally for a full response
            response = await asyncio.to_thread(runnable.invoke, inputs)
            yield response

    except Exception as e:
        print(f"An error occurred: {e}")
        yield None


def generate_response(
    runnable, message: str, user_context: str, internet_context: str, username: str
):
    """
    Generates a response from a runnable, incorporating user profile, chat history, and internet context.

    This function retrieves user personality from a local JSON database, combines it with provided
    contexts, and invokes the given runnable to generate a response.

    Args:
        runnable: The runnable object (e.g., Langchain RunnableSequence) to invoke.
        message (str): The user's input message.
        user_context (str): Contextual information about the user from chat history or other sources.
        internet_context (str): Contextual information retrieved from internet searches.
        username (str): The username of the current user.

    Returns:
        dict or None: The response generated by the runnable, or None if an error occurs.
    """
    try:
        with open("userProfileDb.json", "r", encoding="utf-8") as f:
            db = json.load(f)  # Load user profile database from JSON file

        personality_description = db["userData"].get(
            "personality", "None"
        )  # Get personality description from database, default to "None" if not found

        response = runnable.invoke(
            {
                "query": message,
                "user_context": user_context,
                "internet_context": internet_context,
                "name": username,
                "personality": personality_description,
            }
        )  # Invoke the runnable with combined context and user info

        return response
    except Exception as e:
        print(f"An error occurred in generating response: {e}")
        return None


def get_reframed_internet_query(internet_query_reframe_runnable, input: str) -> str:
    """
    Reframes the user's input query to be more suitable for internet searching.

    Uses a provided runnable (presumably a Langchain RunnableSequence designed for query reframing)
    to transform the input query.

    Args:
        internet_query_reframe_runnable: The runnable responsible for reframing internet queries.
        input (str): The original user input query.

    Returns:
        str: The reframed query string.
    """
    reframed_query = internet_query_reframe_runnable.invoke(
        {"query": input}
    )  # Invoke the query reframing runnable
    return reframed_query


def get_search_results(reframed_query: str) -> list[dict]:
    """
    Fetch and clean descriptions from a web search API based on the provided query.

    Utilizes the Brave Search API to retrieve web search results and extracts relevant information
    like title, URL, and description. Cleans the description using `clean_description` helper function.

    Args:
        reframed_query (str): The search query string to be used for internet search.

    Returns:
        list[dict]: A list of dictionaries, where each dictionary represents a search result
                     and contains 'title', 'url', and 'description' (cleaned) keys.
                     Returns an empty list if there's an error during the search or processing.
    """
    try:
        params = {
            "q": reframed_query,  # The search query parameter
        }

        headers = {
            "Accept": "application/json",  # Accept JSON response
            "Accept-Encoding": "gzip",  # Accept gzip encoding for response compression
            "X-Subscription-Token": os.getenv(
                "BRAVE_SUBSCRIPTION_TOKEN"
            ),  # API token for Brave Search
        }

        response = requests.get(
            os.getenv("BRAVE_BASE_URL"), headers=headers, params=params
        )  # Make GET request to Brave Search API

        if response.status_code == 200:
            results = response.json()  # Parse JSON response

            descriptions = []
            for item in results.get("web", {}).get("results", [])[
                :5
            ]:  # Iterate through the top 5 web search results
                descriptions.append(
                    {
                        "title": item.get("title"),  # Extract title
                        "url": item.get("url"),  # Extract URL
                        "description": item.get("description"),  # Extract description
                    }
                )

            clean_descriptions = [
                {
                    "title": entry["title"],
                    "url": entry["url"],
                    "description": clean_description(
                        entry["description"]
                    ),  # Clean the description using helper function
                }
                for entry in descriptions
            ]

            return clean_descriptions

        else:
            raise Exception(
                f"API request failed with status code {response.status_code}: {response.text}"
            )  # Raise exception if API request fails

    except Exception as e:
        print(f"Error fetching or processing descriptions: {e}")
        return []  # Return empty list in case of error


def get_search_summary(internet_summary_runnable, search_results: list[dict]) -> str:
    """
    Summarizes the provided search results using a given runnable.

    Takes a list of search result dictionaries and uses a summarization runnable (e.g., Langchain
    RunnableSequence for summarization) to condense the information into a summary string.

    Args:
        internet_summary_runnable: The runnable responsible for summarizing internet search results.
        search_results (list[dict]): A list of search result dictionaries, typically from `get_search_results`.

    Returns:
        str: A summary of the search results generated by the runnable.
    """
    search_summary = internet_summary_runnable.invoke(
        {"query": search_results}
    )  # Invoke the summary runnable with search results

    return search_summary


def get_chat_history() -> Optional[List[Dict[str, str]]]:
    """
    Retrieve the chat history from the active chat for use with the Ollama backend.

    Reads the chat history from "chatsDb.json" and formats it into a list of dictionaries
    suitable for conversational models, indicating 'user' or 'assistant' role for each message.

    Returns:
        Optional[List[Dict[str, str]]]: Formatted chat history as a list of dictionaries, where each
                                        dictionary has 'role' ('user' or 'assistant') and 'content'
                                        (message text). Returns None if retrieval fails or no active chat exists.
    """
    try:
        with open("chatsDb.json", "r", encoding="utf-8") as f:
            db = json.load(f)  # Load chat database from JSON file

        active_chat_id = db.get("active_chat_id")
        if active_chat_id is None:
            return []  # No active chat, return empty history

        # Find the active chat
        active_chat = next((chat for chat in db["chats"] if chat["id"] == active_chat_id), None)
        if active_chat is None:
            return []  # Active chat not found, return empty history

        messages = active_chat.get("messages", [])  # Get messages from active chat

        formatted_chat_history: List[Dict[str, str]] = [
            {
                "role": "user" if entry["isUser"] else "assistant",
                "content": entry["message"],
            }
            for entry in messages  # Format all messages from active chat
        ]

        return formatted_chat_history

    except Exception as e:
        print(f"Error retrieving chat history: {str(e)}")
        return None  # Return None in case of error


def context_classify(classification_runnable, query):
    try:
        classification = classification_runnable.invoke({"query":query})
        return classification
    except Exception as e:
        print(f"An error occurred in context classification: {e}")
        return None