@import url('https://fonts.googleapis.com/css2?family=Poppins&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Montserrat&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Quicksand&display=swap');
@import 'tailwindcss';

@config "../tailwind.config.js";

@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}

@keyframes tracing-beam {
	0%,
	100% {
		background: linear-gradient(to right, #00b2fe, #005cfe);
	}
	50% {
		background: linear-gradient(to right, #005cfe, #00b2fe);
	}
}

.tracing-beam {
	@apply bg-linear-to-r from-[#00B2FE] to-[#005CFE] border border-transparent rounded-lg;
	animation: tracing-beam 2s infinite alternate ease-in-out;
}

.gradient-text {
	background: linear-gradient(180deg, #0057ff 0%, #00b3ff 74.17%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

.gradient-text-sideways {
	background: linear-gradient(270deg, #0057ff 2.48%, #00b3ff 74.81%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

.gradient-bg {
	background: linear-gradient(180deg, #0057ff 0%, #00b3ff 74.17%);
}

.text-input {
	@apply w-full h-10 px-4 bg-transparent border border-gray-300 rounded-xl focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.hover-button {
	@apply bg-[#212121] border border-white hover:border-[#00b3ff];
}

.no-scrollbar::-webkit-scrollbar {
	display: none;
}

.no-scrollbar {
	-ms-overflow-style: none;
	scrollbar-width: none;
}

.prose {
	white-space: pre-line;
}

.prose code {
	background-color: black;
	padding: 2px 6px;
	border-radius: 4px;
	font-size: 0.9rem;
	color: white;
}

.prose pre {
	background-color: #2d2d2d;
	padding: 1rem;
	border-radius: 6px;
	overflow-x: auto;
}

.prose pre code {
	color: white;
	font-family: "Source Code Pro", monospace;
}

input[type="date"]::-webkit-calendar-picker-indicator {
	filter: invert(1);
	cursor: pointer;
}
input[type="date"]::-webkit-calendar-picker-indicator:hover {
	filter: invert(0.8);
}

.vis-button {
	background-color: #0057ff !important;

	border: 2px solid #00b2fe !important;

	fill: #ffffff !important;

	width: 40px !important;
	height: 40px !important;

	border-radius: 5px !important;
}

.vis-button:hover {
	background-color: #00b2fe !important;

	border-color: #66ccff !important;

	fill: #ffffff !important;
}

.dot {
	width: 10px;
	height: 10px;
	background-color: white;
	border-radius: 50%;
	animation: dotAnimation 0.6s infinite ease-in-out;
}

.dot1 {
	animation-delay: 0s;
}

.dot2 {
	animation-delay: 0.5s;
}

.dot3 {
	animation-delay: 1s;
}

@keyframes dotAnimation {
	0% {
		transform: translateY(0);
	}
	50% {
		transform: translateY(-5px);
	}
	100% {
		transform: translateY(0);
	}
}
