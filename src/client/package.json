{"private": true, "name": "client", "description": "a completely private, decentralized and interactive AI companion", "version": "1.4.4", "author": "existence", "main": "main/index.js", "exports": "./index.js", "type": "module", "scripts": {"dev": "concurrently -n \"NEXT,ELECTRON\" -c \"yellow,blue\" --kill-others \"next dev\" \"electron .\"", "build": "next build && electron-builder -c electron-builder.yml -p always", "lint": "eslint .", "format": "prettier --check .", "format:fix": "prettier --write ."}, "dependencies": {"@electron-toolkit/utils": "^4.0.0", "@electron/asar": "^3.3.1", "@radix-ui/react-switch": "^1.1.3", "@tabler/icons-react": "^3.30.0", "@tailwindcss/oxide-win32-x64-msvc": "^4.1.1", "@tailwindcss/postcss": "^4.0.7", "@tsparticles/engine": "^3.8.1", "@tsparticles/react": "^3.0.0", "clsx": "^2.1.1", "csv-parser": "^3.2.0", "csv-writer": "^1.6.0", "d3": "^7.9.0", "dotenv": "^16.4.7", "dotenv-vault": "^1.26.2", "electron-serve": "^2.1.1", "electron-store": "^10.0.1", "electron-updater": "^6.3.9", "framer-motion": "^12.4.3", "get-port-please": "^3.1.2", "jwt-decode": "^4.0.0", "keytar": "^7.9.0", "lowdb": "^7.0.1", "lucide-react": "^0.475.0", "neo4j-driver": "^5.28.1", "next": "^15.1.7", "node-fetch": "^3.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-markdown": "^9.0.3", "react-tooltip": "^5.28.0", "rehype-prism": "^2.3.3", "rehype-raw": "^7.0.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "simplex-noise": "^4.0.3", "sudo-prompt": "^9.2.1", "tailwind-merge": "^2.5.4", "tsparticles": "^3.8.1", "uuid": "^9.0.0", "vis-network": "^9.1.9", "webrtc-adapter": "^9.0.1", "ws": "^8.18.1"}, "devDependencies": {"@eslint/js": "^9.20.0", "autoprefixer": "^10.4.20", "concurrently": "^9.1.2", "electron": "^34.2.0", "electron-builder": "^25.1.8", "electron-rebuild": "^3.2.9", "eslint": "^8.57.1", "eslint-config-next": "^15.1.7", "eslint-plugin-react": "^7.37.4", "globals": "^15.15.0", "jest": "^29.7.0", "postcss": "^8.5.2", "prettier": "^3.2.5", "tailwindcss": "^4.0.7", "typescript": "^5.5.0"}}