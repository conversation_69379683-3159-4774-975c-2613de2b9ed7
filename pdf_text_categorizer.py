#!/usr/bin/env python3
"""
PDF Text Categorizer

This script takes a PDF file as input, extracts its text content, and uses an LLM to categorize
the text into predefined categories. It demonstrates the text dissection functionality in isolation.

Usage:
    python pdf_text_categorizer.py path/to/your/file.pdf

Requirements:
    - PyPDF2
    - langchain or langchain_openai (depending on your LLM choice)
    - dotenv
"""

import os
import sys
import json
import argparse
from typing import Dict, Any, List
import PyPDF2
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Check if using OpenAI or another LLM provider
USE_OPENAI = os.getenv("USE_OPENAI", "true").lower() == "true"

if USE_OPENAI:
    from langchain_openai import ChatOpenAI
    from langchain.schema import HumanMessage, SystemMessage
else:
    # Alternative LLM provider (e.g., local model)
    from langchain_community.llms import Ollama

# Predefined categories for text dissection
CATEGORIES = [
    {"name": "Personal Information", "description": "Personal details, preferences, and biographical information"},
    {"name": "Work/Professional", "description": "Career information, job details, professional skills"},
    {"name": "Education", "description": "Academic background, courses, degrees, learning"},
    {"name": "Interests/Hobbies", "description": "Personal interests, hobbies, leisure activities"},
    {"name": "Projects", "description": "Work or personal projects, initiatives, and their details"},
    {"name": "Events/Calendar", "description": "Upcoming or past events, appointments, schedules"},
    {"name": "Contacts/Relationships", "description": "Information about people, relationships, connections"},
    {"name": "Miscellaneous", "description": "Information that doesn't fit into other categories"}
]

def extract_text_from_pdf(pdf_path: str) -> str:
    """
    Extract text content from a PDF file.
    
    Args:
        pdf_path (str): Path to the PDF file
        
    Returns:
        str: Extracted text content
    """
    text = ""
    try:
        with open(pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            for page_num in range(len(reader.pages)):
                page = reader.pages[page_num]
                text += page.extract_text() + "\n"
        return text
    except Exception as e:
        print(f"Error extracting text from PDF: {e}")
        return ""

def get_text_dissection_llm():
    """
    Create and configure an LLM for text dissection tasks.
    
    Returns:
        An LLM instance configured for text dissection
    """
    if USE_OPENAI:
        # Using OpenAI
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            raise ValueError("OPENAI_API_KEY environment variable is not set")
        
        return ChatOpenAI(
            model_name=os.getenv("OPENAI_MODEL_NAME", "gpt-4"),
            temperature=0.2,  # Low temperature for more deterministic outputs
            api_key=api_key
        )
    else:
        # Using local Ollama model
        return Ollama(
            model=os.getenv("OLLAMA_MODEL_NAME", "llama2"),
            temperature=0.2
        )

def create_text_dissection_prompt(user_name: str, text: str) -> Dict[str, Any]:
    """
    Create the prompt for text dissection.
    
    Args:
        user_name (str): Name of the user (for personalization)
        text (str): Text content to be categorized
        
    Returns:
        Dict[str, Any]: Prompt messages for the LLM
    """
    # System prompt explaining the task and expected output format
    system_prompt = f"""You are an advanced text categorization system. Your task is to dissect the provided text into predefined categories.

The available categories are:
{json.dumps([cat["name"] for cat in CATEGORIES], indent=2)}

For each category, extract relevant information from the text. If a category has no relevant information, assign an empty string to it.

Return your response as a JSON object where:
- Keys are the category names
- Values are the extracted text segments relevant to each category

Example output format:
{{
  "Personal Information": "John is 35 years old and lives in New York.",
  "Work/Professional": "He works as a software engineer at TechCorp.",
  "Education": "He has a degree in Computer Science from NYU.",
  "Interests/Hobbies": "John enjoys hiking and photography.",
  "Projects": "",
  "Events/Calendar": "",
  "Contacts/Relationships": "",
  "Miscellaneous": ""
}}

Focus on accurately categorizing the information. The text is about {user_name}.
"""

    # Human message containing the text to categorize
    human_prompt = f"Text: {text}\n\nPlease categorize this text into the predefined categories."
    
    if USE_OPENAI:
        return {
            "messages": [
                SystemMessage(content=system_prompt),
                HumanMessage(content=human_prompt)
            ]
        }
    else:
        return {
            "prompt": f"{system_prompt}\n\n{human_prompt}"
        }

def dissect_text_into_categories(user_name: str, text: str) -> Dict[str, str]:
    """
    Dissect text into predefined categories using an LLM.
    
    Args:
        user_name (str): Name of the user (for personalization)
        text (str): Text content to be categorized
        
    Returns:
        Dict[str, str]: Dictionary mapping categories to relevant text segments
    """
    try:
        # Get the LLM
        llm = get_text_dissection_llm()
        
        # Create the prompt
        prompt = create_text_dissection_prompt(user_name, text)
        
        # Invoke the LLM
        if USE_OPENAI:
            response = llm.invoke(prompt["messages"])
            result = json.loads(response.content)
        else:
            response = llm.invoke(prompt["prompt"])
            # Parse JSON from the response
            result = json.loads(response)
        
        return result
    except Exception as e:
        print(f"Error dissecting text into categories: {e}")
        return {cat["name"]: "" for cat in CATEGORIES}

def main():
    """Main function to run the script."""
    parser = argparse.ArgumentParser(description="Extract and categorize text from a PDF file")
    parser.add_argument("pdf_path", help="Path to the PDF file")
    parser.add_argument("--user_name", default="the user", help="Name of the user for personalization")
    args = parser.parse_args()
    
    # Extract text from PDF
    print(f"Extracting text from {args.pdf_path}...")
    text = extract_text_from_pdf(args.pdf_path)
    
    if not text:
        print("Failed to extract text from the PDF.")
        sys.exit(1)
    
    print(f"Extracted {len(text)} characters of text.")
    
    # Dissect text into categories
    print(f"Categorizing text for user '{args.user_name}'...")
    categories = dissect_text_into_categories(args.user_name, text)
    
    # Print results
    print("\nCategorization Results:")
    print("======================")
    for category, content in categories.items():
        if content:
            print(f"\n{category}:")
            print("-" * len(category))
            print(content[:200] + "..." if len(content) > 200 else content)
    
    # Save results to JSON file
    output_file = os.path.splitext(args.pdf_path)[0] + "_categories.json"
    with open(output_file, 'w') as f:
        json.dump(categories, f, indent=2)
    
    print(f"\nFull categorization results saved to {output_file}")

if __name__ == "__main__":
    main()

"""
The Role of user_name in Text Dissection:

The user_name parameter serves several important purposes in the text dissection function:

1. Personalization: It helps the LLM understand who the text is about, which is crucial for
   correctly interpreting personal information. For example, "I like hiking" vs "John likes hiking".

2. Context Setting: It provides context for the LLM to better understand relationships and
   references in the text. For instance, when the text mentions "his project", the LLM knows
   whose project it is referring to.

3. Pronoun Resolution: It helps resolve pronouns in the text by providing a reference point.
   This is particularly important when extracting information about the user from documents
   that might refer to them in the third person.

4. Ownership Attribution: It helps attribute information, preferences, and actions to the
   correct person, especially when multiple people are mentioned in the text.

In the original system, this parameter is important because the knowledge graph is centered
around a user node, and all extracted information needs to be correctly associated with
either the user or other entities in relation to the user.
"""
