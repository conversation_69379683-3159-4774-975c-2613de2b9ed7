"""
Entity Relationships Module

This module defines relationship types between different entities in an enterprise context.
It provides structured definitions for how entities can be connected in the knowledge graph.
"""

from typing import Dict, List, Any, Tuple

# Common relationship types that apply across many entity pairs
COMMON_RELATIONSHIPS = [
    {
        "name": "RELATED_TO",
        "description": "A generic relationship indicating entities are connected",
        "properties": ["strength", "context", "notes"]
    },
    {
        "name": "MENTIONED_IN",
        "description": "<PERSON><PERSON><PERSON> is referenced or mentioned in another entity",
        "properties": ["context", "sentiment", "timestamp"]
    },
    {
        "name": "CREATED_BY",
        "description": "Entity was created by another entity (typically a Person)",
        "properties": ["timestamp", "tool", "context"]
    },
    {
        "name": "MODIFIED_BY",
        "description": "Entity was modified by another entity (typically a Person)",
        "properties": ["timestamp", "changes", "reason"]
    }
]

# Person to Person relationships
PERSON_TO_PERSON_RELATIONSHIPS = [
    {
        "name": "REPORTS_TO",
        "description": "Indicates a reporting relationship in an organization",
        "properties": ["since", "department", "level"]
    },
    {
        "name": "COLLABORATES_WITH",
        "description": "Indicates people working together",
        "properties": ["projects", "frequency", "effectiveness"]
    },
    {
        "name": "COMMUNICATES_WITH",
        "description": "Indicates communication between people",
        "properties": ["channels", "frequency", "last_contact"]
    },
    {
        "name": "MENTORS",
        "description": "Indicates a mentoring relationship",
        "properties": ["since", "focus_areas", "frequency"]
    }
]

# Person to Company relationships
PERSON_TO_COMPANY_RELATIONSHIPS = [
    {
        "name": "WORKS_FOR",
        "description": "Indicates employment relationship",
        "properties": ["role", "department", "start_date", "end_date"]
    },
    {
        "name": "CONSULTS_FOR",
        "description": "Indicates consulting relationship",
        "properties": ["project", "start_date", "end_date", "contract_value"]
    },
    {
        "name": "INVESTED_IN",
        "description": "Indicates investment relationship",
        "properties": ["amount", "date", "equity_percentage"]
    }
]

# Person to Project relationships
PERSON_TO_PROJECT_RELATIONSHIPS = [
    {
        "name": "MANAGES",
        "description": "Person manages the project",
        "properties": ["role", "since", "responsibilities"]
    },
    {
        "name": "CONTRIBUTES_TO",
        "description": "Person contributes to the project",
        "properties": ["role", "tasks", "time_allocation"]
    },
    {
        "name": "SPONSORS",
        "description": "Person sponsors the project",
        "properties": ["level", "budget_approved", "expectations"]
    },
    {
        "name": "STAKEHOLDER_IN",
        "description": "Person is a stakeholder in the project",
        "properties": ["interest", "influence", "requirements"]
    }
]

# Person to JiraIssue relationships
PERSON_TO_JIRA_RELATIONSHIPS = [
    {
        "name": "ASSIGNED_TO",
        "description": "Person is assigned to the Jira issue",
        "properties": ["date_assigned", "status", "estimated_hours"]
    },
    {
        "name": "REPORTED",
        "description": "Person reported the Jira issue",
        "properties": ["date_reported", "priority_set", "details_provided"]
    },
    {
        "name": "COMMENTED_ON",
        "description": "Person commented on the Jira issue",
        "properties": ["timestamp", "content", "sentiment"]
    },
    {
        "name": "WATCHED_BY",
        "description": "Person is watching the Jira issue",
        "properties": ["since", "notification_settings"]
    }
]

# Project to JiraIssue relationships
PROJECT_TO_JIRA_RELATIONSHIPS = [
    {
        "name": "CONTAINS",
        "description": "Project contains the Jira issue",
        "properties": ["component", "epic", "priority_in_project"]
    },
    {
        "name": "TRACKED_BY",
        "description": "Project progress is tracked by the Jira issue",
        "properties": ["metric_type", "importance", "visibility"]
    }
]

# JiraIssue to JiraIssue relationships
JIRA_TO_JIRA_RELATIONSHIPS = [
    {
        "name": "BLOCKS",
        "description": "Issue blocks another issue",
        "properties": ["reason", "severity", "estimated_resolution_date"]
    },
    {
        "name": "DEPENDS_ON",
        "description": "Issue depends on another issue",
        "properties": ["criticality", "nature_of_dependency"]
    },
    {
        "name": "DUPLICATES",
        "description": "Issue duplicates another issue",
        "properties": ["similarity_score", "resolution_approach"]
    },
    {
        "name": "RELATES_TO",
        "description": "Issue relates to another issue",
        "properties": ["relationship_type", "notes"]
    },
    {
        "name": "PARENT_OF",
        "description": "Issue is a parent of another issue (epic/story relationship)",
        "properties": ["hierarchy_level", "scope_percentage"]
    }
]

# Company to Project relationships
COMPANY_TO_PROJECT_RELATIONSHIPS = [
    {
        "name": "FUNDS",
        "description": "Company funds the project",
        "properties": ["budget", "funding_period", "ROI_expectations"]
    },
    {
        "name": "OWNS",
        "description": "Company owns the project",
        "properties": ["ownership_type", "strategic_importance", "visibility"]
    },
    {
        "name": "BENEFITS_FROM",
        "description": "Company benefits from the project",
        "properties": ["benefit_type", "value", "realization_timeline"]
    }
]

# Company to Company relationships
COMPANY_TO_COMPANY_RELATIONSHIPS = [
    {
        "name": "PARTNERS_WITH",
        "description": "Companies have a partnership",
        "properties": ["partnership_type", "start_date", "end_date", "value"]
    },
    {
        "name": "COMPETES_WITH",
        "description": "Companies are competitors",
        "properties": ["market_segment", "competitive_position", "threat_level"]
    },
    {
        "name": "SUPPLIES_TO",
        "description": "Company supplies to another company",
        "properties": ["products", "contract_value", "contract_period"]
    },
    {
        "name": "SUBSIDIARY_OF",
        "description": "Company is a subsidiary of another company",
        "properties": ["ownership_percentage", "acquisition_date", "autonomy_level"]
    }
]

# Slack conversation relationships
SLACK_RELATIONSHIPS = [
    {
        "name": "DISCUSSES",
        "description": "Conversation discusses an entity",
        "properties": ["context", "sentiment", "resolution"]
    },
    {
        "name": "MENTIONS",
        "description": "Conversation mentions an entity",
        "properties": ["frequency", "context", "importance"]
    },
    {
        "name": "PARTICIPATED_IN",
        "description": "Person participated in conversation",
        "properties": ["contribution_level", "sentiment", "key_points"]
    }
]

# Google Drive document relationships
GDRIVE_RELATIONSHIPS = [
    {
        "name": "AUTHORED",
        "description": "Person authored the document",
        "properties": ["timestamp", "contribution_percentage", "sections"]
    },
    {
        "name": "EDITED",
        "description": "Person edited the document",
        "properties": ["timestamp", "changes", "sections"]
    },
    {
        "name": "COMMENTED_ON",
        "description": "Person commented on the document",
        "properties": ["timestamp", "content", "resolved"]
    },
    {
        "name": "DOCUMENTS",
        "description": "Document contains information about an entity",
        "properties": ["sections", "detail_level", "accuracy"]
    },
    {
        "name": "RELATED_TO",
        "description": "Document is related to another document",
        "properties": ["relationship_type", "importance", "context"]
    }
]

# Meeting relationships
MEETING_RELATIONSHIPS = [
    {
        "name": "ATTENDED",
        "description": "Person attended the meeting",
        "properties": ["role", "contribution", "attendance_status"]
    },
    {
        "name": "ORGANIZED",
        "description": "Person organized the meeting",
        "properties": ["preparation_quality", "materials_provided"]
    },
    {
        "name": "DISCUSSES",
        "description": "Meeting discusses an entity",
        "properties": ["agenda_item", "duration", "outcome"]
    },
    {
        "name": "RESULTED_IN",
        "description": "Meeting resulted in an action or decision",
        "properties": ["importance", "follow_up_required", "status"]
    }
]

# Define valid relationship types between entity pairs
ENTITY_RELATIONSHIP_MAP = {
    ("Person", "Person"): PERSON_TO_PERSON_RELATIONSHIPS,
    ("Person", "Company"): PERSON_TO_COMPANY_RELATIONSHIPS,
    ("Person", "Project"): PERSON_TO_PROJECT_RELATIONSHIPS,
    ("Person", "JiraIssue"): PERSON_TO_JIRA_RELATIONSHIPS,
    ("Project", "JiraIssue"): PROJECT_TO_JIRA_RELATIONSHIPS,
    ("JiraIssue", "JiraIssue"): JIRA_TO_JIRA_RELATIONSHIPS,
    ("Company", "Project"): COMPANY_TO_PROJECT_RELATIONSHIPS,
    ("Company", "Company"): COMPANY_TO_COMPANY_RELATIONSHIPS,
    ("Person", "SlackConversation"): SLACK_RELATIONSHIPS,
    ("Person", "GDriveDocument"): GDRIVE_RELATIONSHIPS,
    ("Person", "Meeting"): MEETING_RELATIONSHIPS
}

# Add common relationships to all entity pairs
for key in ENTITY_RELATIONSHIP_MAP:
    ENTITY_RELATIONSHIP_MAP[key] = ENTITY_RELATIONSHIP_MAP[key] + COMMON_RELATIONSHIPS

# Define relationship extraction rules based on data source
DATA_SOURCE_RELATIONSHIP_RULES = {
    "jira": [
        {
            "source_type": "Person",
            "target_type": "JiraIssue",
            "source_attribute": "id",
            "target_attribute": "attributes.Assignee",
            "relationship": "ASSIGNED_TO",
            "condition": "equals"
        },
        {
            "source_type": "Person",
            "target_type": "JiraIssue",
            "source_attribute": "id",
            "target_attribute": "attributes.Reporter",
            "relationship": "REPORTED",
            "condition": "equals"
        },
        {
            "source_type": "Project",
            "target_type": "JiraIssue",
            "source_attribute": "id",
            "target_attribute": "attributes.Project",
            "relationship": "CONTAINS",
            "condition": "equals"
        }
    ],
    "slack": [
        {
            "source_type": "Person",
            "target_type": "SlackConversation",
            "source_attribute": "id",
            "target_attribute": "attributes.participants",
            "relationship": "PARTICIPATED_IN",
            "condition": "in_list"
        }
    ],
    "gdrive": [
        {
            "source_type": "Person",
            "target_type": "GDriveDocument",
            "source_attribute": "id",
            "target_attribute": "attributes.author",
            "relationship": "AUTHORED",
            "condition": "equals"
        },
        {
            "source_type": "Person",
            "target_type": "GDriveDocument",
            "source_attribute": "id",
            "target_attribute": "attributes.editors",
            "relationship": "EDITED",
            "condition": "in_list"
        }
    ]
}

def get_relationship_types(source_type: str, target_type: str) -> List[Dict[str, Any]]:
    """
    Get valid relationship types between two entity types.
    
    Args:
        source_type: The type of the source entity
        target_type: The type of the target entity
        
    Returns:
        A list of relationship dictionaries with name, description, and properties
    """
    # Check direct mapping
    relationships = ENTITY_RELATIONSHIP_MAP.get((source_type, target_type), [])
    
    # Check reverse mapping (if relationship is bidirectional)
    if not relationships:
        relationships = ENTITY_RELATIONSHIP_MAP.get((target_type, source_type), [])
    
    # If still no relationships found, return common relationships
    if not relationships:
        return COMMON_RELATIONSHIPS
        
    return relationships

def get_relationship_rules(data_source: str) -> List[Dict[str, Any]]:
    """
    Get relationship extraction rules for a specific data source.
    
    Args:
        data_source: The source of the data (e.g., "jira", "slack", "gdrive")
        
    Returns:
        A list of relationship extraction rules
    """
    return DATA_SOURCE_RELATIONSHIP_RULES.get(data_source.lower(), [])

def extract_relationships_from_entities(entities: List[Dict[str, Any]], data_source: str) -> List[Dict[str, Any]]:
    """
    Extract relationships from a list of entities based on predefined rules.
    
    Args:
        entities: List of entity dictionaries
        data_source: The source of the data
        
    Returns:
        A list of relationship dictionaries
    """
    relationships = []
    rules = get_relationship_rules(data_source)
    
    # Create a lookup dictionary for entities by type and id
    entity_lookup = {}
    for entity in entities:
        entity_type = entity.get("entity", {}).get("type")
        entity_id = entity.get("entity", {}).get("id")
        if entity_type and entity_id:
            if entity_type not in entity_lookup:
                entity_lookup[entity_type] = {}
            entity_lookup[entity_type][entity_id] = entity
    
    # Apply relationship extraction rules
    for rule in rules:
        source_type = rule.get("source_type")
        target_type = rule.get("target_type")
        
        if source_type not in entity_lookup or target_type not in entity_lookup:
            continue
            
        source_attribute = rule.get("source_attribute")
        target_attribute = rule.get("target_attribute")
        relationship_type = rule.get("relationship")
        condition = rule.get("condition")
        
        for source_id, source_entity in entity_lookup[source_type].items():
            for target_id, target_entity in entity_lookup[target_type].items():
                # Skip self-relationships
                if source_type == target_type and source_id == target_id:
                    continue
                    
                # Get target attribute value (supports nested attributes with dot notation)
                target_value = target_entity
                for attr in target_attribute.split('.'):
                    if isinstance(target_value, dict) and attr in target_value:
                        target_value = target_value[attr]
                    else:
                        target_value = None
                        break
                
                # Skip if target value is None
                if target_value is None:
                    continue
                
                # Check condition
                match = False
                if condition == "equals" and source_id == target_value:
                    match = True
                elif condition == "in_list" and isinstance(target_value, list) and source_id in target_value:
                    match = True
                
                # If match, create relationship
                if match:
                    relationship = {
                        "source": source_id,
                        "target": target_id,
                        "relationship": relationship_type,
                        "properties": {
                            "data_source": data_source
                        }
                    }
                    relationships.append(relationship)
    
    return relationships
