#!/usr/bin/env python3
"""
Enterprise Knowledge Graph Builder

This script takes a JSON file containing entities and categories (as generated by text_caateg.py)
and creates a knowledge graph in Neo4j. It creates a company node as the root node and connects
all other entities to it based on their categories and relationships.

Usage:
    python enterprise_knowledge_graph_builder.py --input entities.json --company "Acme Corp"

Requirements:
    - neo4j
    - sentence_transformers (for embeddings)
    - dotenv
"""

import os
import sys
import json
import argparse
from typing import Dict, List, Any, Optional
import logging
from neo4j import GraphDatabase
from sentence_transformers import SentenceTransformer
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

class EnterpriseKnowledgeGraphBuilder:
    """
    A class to build an enterprise knowledge graph in Neo4j from entity data.
    """
    
    def __init__(self, uri: str, username: str, password: str, embedding_model: str = "all-MiniLM-L6-v2"):
        """
        Initialize the knowledge graph builder.
        
        Args:
            uri (str): Neo4j database URI
            username (str): Neo4j username
            password (str): Neo4j password
            embedding_model (str): Name of the sentence transformer model for embeddings
        """
        self.driver = GraphDatabase.driver(uri, auth=(username, password))
        logger.info(f"Connected to Neo4j database at {uri}")
        
        # Initialize embedding model
        self.embed_model = SentenceTransformer(embedding_model)
        logger.info(f"Initialized embedding model: {embedding_model}")
        
        # Verify connection
        self._verify_connection()
    
    def _verify_connection(self):
        """Verify connection to Neo4j database."""
        try:
            with self.driver.session() as session:
                result = session.run("RETURN 1 AS num")
                assert result.single()["num"] == 1
                logger.info("Neo4j connection verified successfully")
        except Exception as e:
            logger.error(f"Failed to connect to Neo4j: {e}")
            raise
    
    def close(self):
        """Close the Neo4j driver connection."""
        self.driver.close()
        logger.info("Neo4j connection closed")
    
    def clear_database(self):
        """Clear all nodes and relationships in the database."""
        with self.driver.session() as session:
            session.run("MATCH (n) DETACH DELETE n")
        logger.info("Database cleared")
    
    def get_embedding(self, text: str) -> List[float]:
        """
        Generate an embedding for the given text.
        
        Args:
            text (str): Text to embed
            
        Returns:
            List[float]: Embedding vector
        """
        if not text:
            text = "unknown"
        return self.embed_model.encode(text).tolist()
    
    def create_company_node(self, company_name: str, company_description: str = "") -> int:
        """
        Create the root company node.
        
        Args:
            company_name (str): Name of the company
            company_description (str): Description of the company
            
        Returns:
            int: ID of the created node
        """
        with self.driver.session() as session:
            result = session.run(
                """
                MERGE (c:Company {name: $name})
                SET c.description = $description,
                    c.title_embedding = $title_embedding,
                    c.description_embedding = $description_embedding
                RETURN id(c) AS node_id
                """,
                name=company_name,
                description=company_description or f"Company: {company_name}",
                title_embedding=self.get_embedding(company_name),
                description_embedding=self.get_embedding(company_description or f"Company: {company_name}")
            )
            node_id = result.single()["node_id"]
            logger.info(f"Created company node: {company_name} (ID: {node_id})")
            return node_id
    
    def create_category_nodes(self, categories: List[Dict[str, str]], company_id: int) -> Dict[str, int]:
        """
        Create category nodes and link them to the company node.
        
        Args:
            categories (List[Dict[str, str]]): List of category dictionaries with name and description
            company_id (int): ID of the company node
            
        Returns:
            Dict[str, int]: Dictionary mapping category names to their node IDs
        """
        category_ids = {}
        with self.driver.session() as session:
            for category in categories:
                name = category["name"]
                description = category.get("description", f"Category: {name}")
                
                result = session.run(
                    """
                    MERGE (c:Category {name: $name})
                    SET c.description = $description,
                        c.title_embedding = $title_embedding,
                        c.description_embedding = $description_embedding
                    WITH c
                    MATCH (company) WHERE id(company) = $company_id
                    MERGE (company)-[:HAS_CATEGORY]->(c)
                    RETURN id(c) AS node_id
                    """,
                    name=name,
                    description=description,
                    title_embedding=self.get_embedding(name),
                    description_embedding=self.get_embedding(description),
                    company_id=company_id
                )
                node_id = result.single()["node_id"]
                category_ids[name] = node_id
                logger.info(f"Created category node: {name} (ID: {node_id})")
            
        return category_ids
    
    def create_entity_nodes(self, entities: List[Dict[str, Any]], category_ids: Dict[str, int]) -> Dict[str, int]:
        """
        Create entity nodes and link them to their categories.
        
        Args:
            entities (List[Dict[str, Any]]): List of entity dictionaries
            category_ids (Dict[str, int]): Dictionary mapping category names to their node IDs
            
        Returns:
            Dict[str, int]: Dictionary mapping entity names to their node IDs
        """
        entity_ids = {}
        with self.driver.session() as session:
            for entity in entities:
                name = entity["name"]
                entity_type = entity.get("entity_type", "Generic")
                category = entity.get("category", "Miscellaneous")
                description = entity.get("description", f"{entity_type}: {name}")
                attributes = entity.get("attributes", {})
                
                # Sanitize attributes (ensure all values are strings)
                sanitized_attributes = {k: str(v) if v is not None else "" for k, v in attributes.items()}
                
                # Add entity type as a property
                sanitized_attributes["entity_type"] = entity_type
                
                # Get category ID, default to Miscellaneous if not found
                category_id = category_ids.get(category)
                if not category_id and "Miscellaneous" in category_ids:
                    category_id = category_ids["Miscellaneous"]
                    category = "Miscellaneous"
                    logger.warning(f"Category not found for entity {name}, using Miscellaneous")
                
                if not category_id:
                    logger.error(f"Cannot create entity {name}: no valid category found")
                    continue
                
                result = session.run(
                    """
                    MERGE (e:Entity {name: $name})
                    SET e += $attributes,
                        e.description = $description,
                        e.category = $category,
                        e.title_embedding = $title_embedding,
                        e.description_embedding = $description_embedding
                    WITH e
                    MATCH (c:Category) WHERE id(c) = $category_id
                    MERGE (c)-[:HAS_ENTITY]->(e)
                    RETURN id(e) AS node_id
                    """,
                    name=name,
                    attributes=sanitized_attributes,
                    description=description,
                    category=category,
                    title_embedding=self.get_embedding(name),
                    description_embedding=self.get_embedding(description),
                    category_id=category_id
                )
                node_id = result.single()["node_id"]
                entity_ids[name] = node_id
                logger.info(f"Created entity node: {name} (ID: {node_id})")
            
        return entity_ids
    
    def create_relationships(self, relationships: List[Dict[str, Any]], entity_ids: Dict[str, int]):
        """
        Create relationships between entities.
        
        Args:
            relationships (List[Dict[str, Any]]): List of relationship dictionaries
            entity_ids (Dict[str, int]): Dictionary mapping entity names to their node IDs
        """
        with self.driver.session() as session:
            for rel in relationships:
                source = rel["source"]
                target = rel["target"]
                rel_type = rel["relationship"]
                properties = rel.get("properties", {})
                
                # Skip if source or target entity doesn't exist
                if source not in entity_ids or target not in entity_ids:
                    logger.warning(f"Skipping relationship {source}-[{rel_type}]->{target}: entities not found")
                    continue
                
                # Sanitize properties
                sanitized_properties = {k: str(v) if v is not None else "" for k, v in properties.items()}
                
                session.run(
                    f"""
                    MATCH (s:Entity) WHERE id(s) = $source_id
                    MATCH (t:Entity) WHERE id(t) = $target_id
                    MERGE (s)-[r:`{rel_type}`]->(t)
                    SET r += $properties
                    """,
                    source_id=entity_ids[source],
                    target_id=entity_ids[target],
                    properties=sanitized_properties
                )
                logger.info(f"Created relationship: {source}-[{rel_type}]->{target}")
    
    def build_knowledge_graph(self, data: Dict[str, Any], company_name: str, company_description: str = ""):
        """
        Build the complete knowledge graph from the provided data.
        
        Args:
            data (Dict[str, Any]): Dictionary containing entities, categories, and relationships
            company_name (str): Name of the company
            company_description (str): Description of the company
        """
        # Extract components from data
        entities = data.get("entities", [])
        categories = data.get("categories", [])
        relationships = data.get("relationships", [])
        
        # Ensure we have a Miscellaneous category
        if not any(cat["name"] == "Miscellaneous" for cat in categories):
            categories.append({
                "name": "Miscellaneous",
                "description": "Information that doesn't fit into other categories"
            })
        
        # Create company node
        company_id = self.create_company_node(company_name, company_description)
        
        # Create category nodes
        category_ids = self.create_category_nodes(categories, company_id)
        
        # Create entity nodes
        entity_ids = self.create_entity_nodes(entities, category_ids)
        
        # Create relationships
        self.create_relationships(relationships, entity_ids)
        
        logger.info(f"Knowledge graph built successfully with {len(entities)} entities, "
                   f"{len(categories)} categories, and {len(relationships)} relationships")

def main():
    """Main function to run the script."""
    parser = argparse.ArgumentParser(description="Build an enterprise knowledge graph in Neo4j")
    parser.add_argument("--input", required=True, help="Path to the input JSON file")
    parser.add_argument("--company", required=True, help="Name of the company (root node)")
    parser.add_argument("--description", default="", help="Description of the company")
    parser.add_argument("--uri", default=os.getenv("NEO4J_URI", "bolt://localhost:7687"), 
                        help="Neo4j database URI")
    parser.add_argument("--username", default=os.getenv("NEO4J_USERNAME", "neo4j"), 
                        help="Neo4j username")
    parser.add_argument("--password", default=os.getenv("NEO4J_PASSWORD", "password"), 
                        help="Neo4j password")
    parser.add_argument("--clear", action="store_true", help="Clear the database before building")
    parser.add_argument("--embedding-model", default="all-MiniLM-L6-v2", 
                        help="Name of the sentence transformer model for embeddings")
    
    args = parser.parse_args()
    
    # Load input data
    try:
        with open(args.input, 'r') as f:
            data = json.load(f)
        logger.info(f"Loaded input data from {args.input}")
    except Exception as e:
        logger.error(f"Failed to load input data: {e}")
        sys.exit(1)
    
    # Initialize knowledge graph builder
    try:
        builder = EnterpriseKnowledgeGraphBuilder(
            uri=args.uri,
            username=args.username,
            password=args.password,
            embedding_model=args.embedding_model
        )
    except Exception as e:
        logger.error(f"Failed to initialize knowledge graph builder: {e}")
        sys.exit(1)
    
    try:
        # Clear database if requested
        if args.clear:
            builder.clear_database()
        
        # Build knowledge graph
        builder.build_knowledge_graph(data, args.company, args.description)
        
        logger.info("Knowledge graph built successfully")
    except Exception as e:
        logger.error(f"Error building knowledge graph: {e}")
        sys.exit(1)
    finally:
        builder.close()

if __name__ == "__main__":
    main()
