# Enterprise Knowledge Graph Builder

This repository contains tools for building an enterprise knowledge graph from unstructured data. The process involves two main steps:

1. **Text Categorization**: Extract entities, attributes, and relationships from unstructured text
2. **Knowledge Graph Construction**: Build a Neo4j knowledge graph from the extracted information

## Components

### 1. PDF Text Categorizer (`pdf_text_categorizer.py`)

This script extracts and categorizes text from PDF documents using an LLM.

#### Features:
- PDF text extraction
- LLM-based text categorization
- Support for OpenAI or local models
- Predefined categories for enterprise data

#### Usage:
```bash
python pdf_text_categorizer.py path/to/your/file.pdf --user_name "Company Name"
```

#### Output:
- Console output with categorized text
- JSON file with full categorization results

### 2. Enterprise Knowledge Graph Builder (`enterprise_knowledge_graph_builder.py`)

This script builds a Neo4j knowledge graph from structured entity data.

#### Features:
- Company-centric graph structure
- Category and entity nodes with relationships
- Semantic embeddings for similarity search
- Flexible entity and relationship types

#### Usage:
```bash
python enterprise_knowledge_graph_builder.py --input entities.json --company "Acme Corp"
```

#### Additional Options:
- `--description`: Company description
- `--uri`: Neo4j database URI
- `--username`: Neo4j username
- `--password`: Neo4j password
- `--clear`: Clear the database before building
- `--embedding-model`: Specify a different sentence transformer model

## Input Format

The knowledge graph builder expects a JSON file with the following structure:

```json
{
  "entities": [
    {
      "name": "Entity Name",
      "entity_type": "Type",
      "category": "Category",
      "description": "Description",
      "attributes": {
        "attribute1": "value1",
        "attribute2": "value2"
      }
    }
  ],
  "categories": [
    {
      "name": "Category Name",
      "description": "Category Description"
    }
  ],
  "relationships": [
    {
      "source": "Source Entity",
      "relationship": "RELATIONSHIP_TYPE",
      "target": "Target Entity",
      "properties": {
        "property1": "value1",
        "property2": "value2"
      }
    }
  ]
}
```

## Knowledge Graph Structure

The resulting Neo4j graph has the following structure:

### Node Labels:
- `Company`: The root node representing the company
- `Category`: Nodes representing categories of information
- `Entity`: Nodes representing extracted entities

### Relationship Types:
- `HAS_CATEGORY`: Connects Company to Category nodes
- `HAS_ENTITY`: Connects Category to Entity nodes
- Custom relationship types: Connect Entity nodes to each other

### Node Properties:
- `name`: Name of the node
- `description`: Description of the node
- `category`: Category the entity belongs to (for Entity nodes)
- `entity_type`: Type of entity (for Entity nodes)
- `title_embedding`: Vector embedding of the node name
- `description_embedding`: Vector embedding of the node description
- Custom attributes: Additional properties specific to each entity

## Example Queries

Once the knowledge graph is built, you can run Cypher queries in Neo4j:

### Find all departments in the company:
```cypher
MATCH (c:Company)-[:HAS_CATEGORY]->(cat:Category)-[:HAS_ENTITY]->(e:Entity)
WHERE e.entity_type = 'Department'
RETURN e.name, e.description, e.attributes
```

### Find employees who lead departments:
```cypher
MATCH (e1:Entity)-[:LEADS]->(e2:Entity)
WHERE e1.entity_type = 'Employee' AND e2.entity_type = 'Department'
RETURN e1.name, e1.attributes.title, e2.name
```

### Find systems used by a specific department:
```cypher
MATCH (dept:Entity)-[:USES]->(system:Entity)
WHERE dept.name = 'Sales Department' AND system.entity_type = 'Software'
RETURN system.name, system.description, system.attributes
```

### Semantic search for entities related to "customer":
```cypher
WITH $query_embedding AS query_embedding
MATCH (e:Entity)
WITH e, gds.similarity.cosine(e.description_embedding, query_embedding) AS score
WHERE score > 0.6
RETURN e.name, e.entity_type, e.description, score
ORDER BY score DESC
LIMIT 5
```

## Requirements

- Python 3.7+
- Neo4j 4.0+
- PyPDF2
- neo4j-python-driver
- sentence-transformers
- langchain or langchain_openai (depending on LLM choice)
- dotenv

## Setup

1. Install required packages:
   ```bash
   pip install neo4j sentence-transformers PyPDF2 python-dotenv langchain langchain_openai
   ```

2. Set up environment variables (or create a `.env` file):
   ```
   NEO4J_URI=bolt://localhost:7687
   NEO4J_USERNAME=neo4j
   NEO4J_PASSWORD=your_password
   OPENAI_API_KEY=your_openai_key  # If using OpenAI
   ```

3. Start Neo4j database

4. Run the scripts as described above

## Sample Data

A sample entities JSON file (`sample_entities.json`) is provided to demonstrate the expected input format for the knowledge graph builder.
