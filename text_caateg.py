#!/usr/bin/env python3
"""
Enterprise Text Categorizer

This script takes text content from various data sources (PDF, Slack, Jira, etc.),
extracts entities, and categorizes the text for each entity into predefined categories.
It supports enterprise-level categorization for knowledge graph integration.

Usage:
    python text_categorizer.py --source pdf --input path/to/your/file.pdf
    python text_categorizer.py --source slack --input path/to/slack/export.json
    python text_categorizer.py --source jira --input path/to/jira/issue.json

Requirements:
    - PyPDF2 (for PDF processing)
    - langchain or langchain_openai (depending on your LLM choice)
    - dotenv
"""

import os
import sys
import json
import argparse
from typing import Dict, Any, List, Optional, Union
import PyPDF2
from dotenv import load_dotenv

# Import entity categories
from entity_categories import (
    get_categories_for_entity_type,
    get_likely_entity_types,
    COMMON_CATEGORIES
)

# Load environment variables from .env file
load_dotenv()

# Check if using OpenAI or another LLM provider
USE_OPENAI = os.getenv("USE_OPENAI", "true").lower() == "true"

if USE_OPENAI:
    from langchain_openai import ChatOpenAI
    from langchain.schema import HumanMessage, SystemMessage
else:
    # Alternative LLM provider (e.g., local model)
    from langchain_community.llms import Ollama

def extract_text_from_pdf(pdf_path: str) -> str:
    """
    Extract text content from a PDF file.

    Args:
        pdf_path (str): Path to the PDF file

    Returns:
        str: Extracted text content
    """
    text = ""
    try:
        with open(pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            for page_num in range(len(reader.pages)):
                page = reader.pages[page_num]
                text += page.extract_text() + "\n"
        return text
    except Exception as e:
        print(f"Error extracting text from PDF: {e}")
        return ""

def _extract_text_from_slack(input_path: str) -> str:
    """Extract text from a Slack export JSON file."""
    try:
        with open(input_path, 'r') as file:
            data = json.load(file)
            if isinstance(data, list):
                return "\n".join([msg.get("text", "") for msg in data if "text" in msg])
            return json.dumps(data)  # Fallback for unexpected format
    except Exception as e:
        print(f"Error extracting text from Slack export: {e}")
        return ""

def _extract_text_from_jira(input_path: str) -> str:
    """Extract text from a Jira export JSON file."""
    try:
        with open(input_path, 'r') as file:
            data = json.load(file)
            all_issues_text = []
            issues = []
            if "issues" in data and isinstance(data["issues"], list):
                issues = data["issues"]
            elif "id" in data and "key" in data and "fields" in data:
                issues = [data]

            for issue in issues:
                issue_text_parts = []
                issue_text_parts.append(f"Issue ID: {issue.get('id', 'Unknown')}")
                issue_text_parts.append(f"Issue Key: {issue.get('key', 'Unknown')}")
                if "fields" in issue:
                    fields = issue["fields"]
                    if "summary" in fields:
                        issue_text_parts.append(f"Summary: {fields['summary']}")
                    if "description" in fields:
                        issue_text_parts.append(f"Description: {fields['description']}")
                    if "issuetype" in fields and isinstance(fields["issuetype"], dict):
                        issue_text_parts.append(f"Issue Type: {fields['issuetype'].get('name', 'Unknown')}")
                    if "status" in fields and isinstance(fields["status"], dict):
                        issue_text_parts.append(f"Status: {fields['status'].get('name', 'Unknown')}")
                    if "priority" in fields and isinstance(fields["priority"], dict):
                        issue_text_parts.append(f"Priority: {fields['priority'].get('name', 'Unknown')}")
                    if "assignee" in fields and fields["assignee"]:
                        assignee = fields["assignee"]
                        assignee_name = assignee.get("displayName", "Unknown")
                        assignee_email = assignee.get("emailAddress", "")
                        issue_text_parts.append(f"Assignee: {assignee_name} ({assignee_email})")
                    else:
                        issue_text_parts.append("Assignee: Unassigned")
                    if "reporter" in fields and fields["reporter"]:
                        reporter = fields["reporter"]
                        reporter_name = reporter.get("displayName", "Unknown")
                        reporter_email = reporter.get("emailAddress", "")
                        issue_text_parts.append(f"Reporter: {reporter_name} ({reporter_email})")
                    if "project" in fields and isinstance(fields["project"], dict):
                        project = fields["project"]
                        project_key = project.get("key", "Unknown")
                        project_name = project.get("name", "Unknown")
                        issue_text_parts.append(f"Project: {project_name} ({project_key})")
                    if "created" in fields:
                        issue_text_parts.append(f"Created: {fields['created']}")
                    if "updated" in fields:
                        issue_text_parts.append(f"Updated: {fields['updated']}")
                    if "duedate" in fields:
                        issue_text_parts.append(f"Due Date: {fields['duedate']}")
                    if "labels" in fields and fields["labels"]:
                        labels_str = ", ".join(fields["labels"])
                        issue_text_parts.append(f"Labels: {labels_str}")
                    if "comment" in fields and "comments" in fields["comment"]:
                        comments_data = fields["comment"]["comments"]
                        if comments_data:
                            issue_text_parts.append("Comments:")
                            for i, comment_item in enumerate(comments_data, 1):
                                author = comment_item.get("author", {}).get("displayName", "Unknown")
                                body = comment_item.get("body", "")
                                created = comment_item.get("created", "")
                                issue_text_parts.append(f"  Comment {i} by {author} on {created}:")
                                issue_text_parts.append(f"  {body}")
                all_issues_text.append("\n".join(issue_text_parts))
            return "\n\n" + "\n\n" + "-" * 50 + "\n\n".join(all_issues_text)
    except Exception as e:
        print(f"Error extracting text from Jira export: {e}")
        return ""

def _extract_text_from_gdrive(input_path: str) -> str:
    """Extract text from a Google Drive export (assumed text file)."""
    try:
        with open(input_path, 'r') as file:
            return file.read()
    except Exception as e:
        print(f"Error extracting text from Google Drive document: {e}")
        return ""

def _extract_text_generic(input_path: str) -> str:
    """Extract text from a generic text file."""
    try:
        with open(input_path, 'r') as file:
            return file.read()
    except Exception as e:
        print(f"Error extracting text from generic file: {e}")
        return ""

# Dispatch dictionary for text extraction
TEXT_EXTRACTORS = {
    "pdf": extract_text_from_pdf,
    "slack": _extract_text_from_slack,
    "jira": _extract_text_from_jira,
    "gdrive": _extract_text_from_gdrive,
}

def extract_text_from_source(source_type: str, input_path: str) -> str:
    """
    Extract text from various data sources using a dispatch table.

    Args:
        source_type: Type of data source (pdf, slack, jira, etc.)
        input_path: Path to the input file

    Returns:
        Extracted text content
    """
    extractor = TEXT_EXTRACTORS.get(source_type.lower())
    if extractor:
        return extractor(input_path)
    else:
        # Default to generic text extraction if source type is unknown
        print(f"Unknown source type '{source_type}'. Attempting generic text extraction.")
        return _extract_text_generic(input_path)

def get_llm():
    """
    Create and configure an LLM for text processing tasks.

    Returns:
        An LLM instance configured for text processing
    """
    if USE_OPENAI:
        # Using OpenAI
        api_key = os.getenv("OPENROUTER_API_KEY")
        if not api_key:
            raise ValueError("OPENROUTER_API_KEY environment variable is not set")

        return ChatOpenAI(
            model_name=os.getenv("OPENAI_MODEL_NAME", "gpt-4"),
            temperature=0.2,  # Low temperature for more deterministic outputs
            api_key=api_key,
            base_url=os.getenv("OPENROUTER_API_BASE", "https://openrouter.ai/api/v1")
        )
    else:
        # Using local Ollama model
        return Ollama(
            model=os.getenv("OLLAMA_MODEL_NAME", "llama2"),
            temperature=0.2
        )

def create_entity_detection_prompt(data_source: str, text: str) -> Dict[str, Any]:
    """
    Create a prompt for entity detection.

    Args:
        data_source: Source of the data (pdf, slack, jira, etc.)
        text: Text content to analyze

    Returns:
        Prompt for the LLM
    """
    likely_entity_types = get_likely_entity_types(data_source)

    system_prompt = f"""You are an advanced entity detection system for enterprise text analysis.
Your task is to identify entities mentioned in the provided text from a {data_source} source.

For this {data_source} content, the most likely entity types are: {', '.join(likely_entity_types)}
However, you should detect ANY relevant entity types, including but not limited to:
- Person: Individual people mentioned
- Company: Organizations, businesses, or corporate entities
- Project: Work initiatives, assignments, or undertakings
- JiraIssue: Software development tasks, bugs, or features
- SlackConversation: Chat discussions or threads
- GDriveDocument: Documents, spreadsheets, or presentations
- Meeting: Scheduled gatherings or discussions

For each entity you detect, provide:
1. type: The entity type (e.g., "Person", "Company", "Project")
2. id: A unique identifier or name for this entity
3. attributes: Any key attributes mentioned about this entity

Return your response as a JSON array of entity objects.

Example output format:
[
  {{
    "type": "Person",
    "id": "John Smith",
    "attributes": {{
      "role": "Project Manager",
      "email": "<EMAIL>"
    }}
  }},
  {{
    "type": "Project",
    "id": "Website Redesign",
    "attributes": {{
      "deadline": "2023-12-31",
      "status": "In Progress"
    }}
  }}
]

If no entities are detected, return an empty array: []
"""

    human_prompt = f"Text from {data_source} source: {text}\n\nPlease identify all entities in this text."

    if USE_OPENAI:
        return {
            "messages": [
                SystemMessage(content=system_prompt),
                HumanMessage(content=human_prompt)
            ]
        }
    else:
        return {
            "prompt": f"{system_prompt}\n\n{human_prompt}"
        }

def detect_entities(data_source: str, text: str) -> List[Dict[str, Any]]:
    """
    Detect entities in the provided text.

    Args:
        data_source: Source of the data (pdf, slack, jira, etc.)
        text: Text content to analyze

    Returns:
        List of detected entities
    """
    try:
        # Get the LLM
        llm = get_llm()

        # Create the prompt
        prompt = create_entity_detection_prompt(data_source, text)

        # Invoke the LLM
        if USE_OPENAI:
            response = llm.invoke(prompt["messages"])
            result = json.loads(response.content)
        else:
            response = llm.invoke(prompt["prompt"])
            # Parse JSON from the response
            result = json.loads(response)

        return result
    except Exception as e:
        print(f"Error detecting entities: {e}")
        # If entity detection fails, create a default entity based on the data source
        default_entity_type = get_likely_entity_types(data_source)[0]
        return [{
            "type": default_entity_type,
            "id": f"Default {default_entity_type}",
            "attributes": {}
        }]

def create_text_categorization_prompt(entity: Dict[str, Any], text: str) -> Dict[str, Any]:
    """
    Create a prompt for text categorization based on entity type.

    Args:
        entity: Entity object with type and id
        text: Text content to categorize

    Returns:
        Prompt for the LLM
    """
    entity_type = entity["type"]
    entity_id = entity["id"]
    categories = get_categories_for_entity_type(entity_type)

    system_prompt = f"""You are an advanced text categorization system for enterprise knowledge graphs.
Your task is to categorize information about a specific entity from the provided text.

Entity Type: {entity_type}
Entity ID: {entity_id}

The available categories for this {entity_type} entity are:
{json.dumps([cat["name"] for cat in categories], indent=2)}

For each category, extract relevant information from the text about this specific entity.
If a category has no relevant information, assign an empty string to it.

Return your response as a JSON object where:
- Keys are the category names
- Values are the extracted text segments relevant to each category

Focus ONLY on information related to this specific {entity_type} entity ({entity_id}).
"""

    human_prompt = f"Text: {text}\n\nPlease categorize information about {entity_type} '{entity_id}' into the predefined categories."

    if USE_OPENAI:
        return {
            "messages": [
                SystemMessage(content=system_prompt),
                HumanMessage(content=human_prompt)
            ]
        }
    else:
        return {
            "prompt": f"{system_prompt}\n\n{human_prompt}"
        }

def categorize_entity_text(entity: Dict[str, Any], text: str) -> Dict[str, str]:
    """
    Categorize text for a specific entity.

    Args:
        entity: Entity object with type and id
        text: Text content to categorize

    Returns:
        Dictionary mapping categories to relevant text segments
    """
    try:
        # Get the LLM
        llm = get_llm()

        # Create the prompt
        prompt = create_text_categorization_prompt(entity, text)

        # Invoke the LLM
        if USE_OPENAI:
            response = llm.invoke(prompt["messages"])
            result = json.loads(response.content)
        else:
            response = llm.invoke(prompt["prompt"])
            # Parse JSON from the response
            result = json.loads(response)

        return result
    except Exception as e:
        print(f"Error categorizing text for {entity['type']} '{entity['id']}': {e}")
        # Return empty categories on error
        categories = get_categories_for_entity_type(entity["type"])
        return {cat["name"]: "" for cat in categories}

def process_text(data_source: str, text: str) -> Dict[str, Any]:
    """
    Process text by detecting entities and categorizing information for each entity.

    Args:
        data_source: Source of the data (pdf, slack, jira, etc.)
        text: Text content to process

    Returns:
        Processed results with entities and their categorized information
    """
    # Detect entities
    entities = detect_entities(data_source, text)

    # Process each entity
    results = []
    for entity in entities:
        # Categorize text for this entity
        categories = categorize_entity_text(entity, text)

        # Add to results
        results.append({
            "entity": entity,
            "categories": categories
        })

    return {
        "status": "success",
        "data_source": data_source,
        "entities": results
    }

def main():
    """Main function to run the script."""
    parser = argparse.ArgumentParser(description="Extract entities and categorize text from various data sources")
    parser.add_argument("--source", required=True, help="Source type (pdf, slack, jira, gdrive, etc.)")
    parser.add_argument("--input", required=True, help="Path to the input file")
    parser.add_argument("--output", help="Path to save the output JSON (default: input_categorized.json)")
    args = parser.parse_args()

    # Extract text from the source
    print(f"Extracting text from {args.source} source: {args.input}...")
    text = extract_text_from_source(args.source, args.input)

    if not text:
        print(f"Failed to extract text from the {args.source} source.")
        sys.exit(1)

    print(f"Extracted {len(text)} characters of text.")

    # Process the text
    print(f"Processing text from {args.source} source...")
    results = process_text(args.source, text)

    # Print summary of results
    print("\nProcessing Results:")
    print("=================")
    for entity_result in results["entities"]:
        entity = entity_result["entity"]
        categories = entity_result["categories"]

        print(f"\nEntity: {entity['type']} - {entity['id']}")
        print("-" * 40)

        for category, content in categories.items():
            if content:
                print(f"\n{category}:")
                print("-" * len(category))
                print(content[:200] + "..." if len(content) > 200 else content)

    # Save results to JSON file
    output_file = args.output if args.output else os.path.splitext(args.input)[0] + "_categorized.json"
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2)

    print(f"\nFull processing results saved to {output_file}")

if __name__ == "__main__":
    main()
