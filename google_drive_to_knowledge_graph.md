# From Google Drive to Knowledge Graph: Detailed Architecture

This document explains how the system connects to Google Drive, extracts unstructured data, processes it, and forms a knowledge graph. It provides a step-by-step explanation of the architecture and processes involved.

## Table of Contents

1. [Data Extraction from Google Drive](#1-data-extraction-from-google-drive)
2. [Text Processing and Categorization](#2-text-processing-and-categorization)
3. [Entity and Relationship Extraction](#3-entity-and-relationship-extraction)
4. [Knowledge Graph Construction in Neo4j](#4-knowledge-graph-construction-in-neo4j)
5. [Querying the Knowledge Graph](#5-querying-the-knowledge-graph)
6. [Complete Example Flow](#6-complete-example-flow)

## 1. Data Extraction from Google Drive

The system connects to Google Drive using the Google Drive API to extract files and their content.

### 1.1 Authentication

```python
def authenticate_drive():
    """Authenticate and return the Google Drive API service."""
    creds = None
    if os.path.exists("server/token.pickle"):
        with open("server/token.pickle", "rb") as token:
            creds = pickle.load(token)
    return build("drive", "v3", credentials=creds)
```

### 1.2 Searching Files

```python
async def search_file_in_gdrive(query: str):
    """Search for files in Google Drive matching the query."""
    service = authenticate_drive()
    
    # Search for files matching query
    results = service.files().list(
        q=f"name contains '{query}' and trashed=false",
        spaces="drive",
        fields="files(id, name, webViewLink, mimeType)"
    ).execute()
    
    files = results.get("files", [])
    return {
        "status": "success",
        "result": {
            "response": f"Found {len(files)} files matching '{query}'",
            "files": files
        }
    }
```

### 1.3 Downloading Files

```python
async def download_file(service, file_id, destination, export_format="application/pdf"):
    """Download a file from Google Drive."""
    # Get file metadata
    file_metadata = service.files().get(fileId=file_id).execute()
    file_name = file_metadata["name"]
    
    # Determine file path
    file_path = os.path.join(
        destination,
        f"{file_name}.pdf" if export_format == "application/pdf" else file_name
    )
    
    # Handle Google Workspace files differently from binary files
    if file_metadata["mimeType"].startswith("application/vnd.google-apps"):
        request = service.files().export_media(fileId=file_id, mimeType=export_format)
    else:
        request = service.files().get_media(fileId=file_id)
    
    # Download the file
    with open(file_path, "wb") as f:
        downloader = MediaIoBaseDownload(f, request)
        done = False
        while not done:
            status, done = downloader.next_chunk()
    
    return file_path
```

## 2. Text Processing and Categorization

After downloading files from Google Drive, the system processes the content to extract meaningful information.

### 2.1 Text Extraction

Depending on the file type, different methods are used to extract text:
- For PDFs: PDF parsing libraries
- For documents: Text extraction from exported formats
- For images: Possibly OCR (Optical Character Recognition)

### 2.2 Text Categorization

The extracted text is categorized using the `text_dissection_runnable`:

```python
def dissect_information_into_categories(user_name, bulk_text, text_dissection_runnable):
    """Dissect bulk unstructured text into predefined categories."""
    try:
        categories = text_dissection_runnable.invoke({
            "user_name": user_name, 
            "text": bulk_text
        })
        return categories
    except Exception as e:
        print(f"Error dissecting information into categories: {e}")
        return {}
```

### 2.3 Predefined Categories

The system uses predefined categories to organize the information:
- Personal Information
- Work/Professional
- Education
- Interests/Hobbies
- Projects
- Events/Calendar
- Contacts/Relationships
- Miscellaneous

## 3. Entity and Relationship Extraction

For each category, the system extracts entities and relationships using LLM-based extraction.

### 3.1 Extraction Process

```python
def extract_entities_and_relationships_for_category(
    category_name, category_text, information_extraction_runnable
):
    """Extract entities and relationships for a specific category."""
    try:
        triplets = information_extraction_runnable.invoke({
            "category": category_name, 
            "text": category_text
        })
        return triplets
    except Exception as e:
        print(f"Error extracting entities and relationships: {e}")
        return []
```

### 3.2 LLM-Based Extraction

The LLM-based extraction identifies:
- **Entities**: People, organizations, locations, concepts, documents, etc.
- **Relationships**: How these entities are connected (e.g., WORKS_AT, AUTHORED, RELATED_TO)
- **Properties**: Attributes of entities and relationships

### 3.3 Example Extraction Output

```json
{
  "source": "Projects",
  "relationship": "HAS_PROJECT",
  "target": "Marketing Campaign 2023",
  "source_properties": {
    "description": "Work projects and initiatives"
  },
  "relationship_properties": {
    "type": "category_to_entity",
    "start_date": "2023-01-15"
  },
  "target_properties": {
    "description": "Social media marketing campaign for Q1 2023",
    "status": "Completed",
    "document_link": "https://drive.google.com/file/d/abc123/view"
  }
}
```

## 4. Knowledge Graph Construction in Neo4j

The system uses Neo4j as the graph database to store the extracted information.

### 4.1 Neo4j Graph Structure

The knowledge graph consists of:

#### Node Labels
- `User`: The root node representing the user
- `Category`: Nodes representing predefined categories
- `Entity`: Nodes representing extracted entities

#### Relationship Types
- `HAS_CATEGORY`: Connects User to Category nodes
- `HAS_ENTITY`: Connects Category to Entity nodes
- Dynamic relationship types based on extracted relationships (e.g., `WORKS_AT`, `AUTHORED`)

#### Node Properties
- `name`: Name of the entity
- `description`: Description of the entity
- `category`: Category the entity belongs to
- `source`: Source of the information (e.g., "Google Drive")
- `title_embedding`: Vector embedding of the entity name
- `description_embedding`: Vector embedding of the entity description
- Other properties specific to the entity

#### Relationship Properties
- `type`: Type of relationship (e.g., "category_to_entity", "entity_to_entity")
- Other properties specific to the relationship

### 4.2 Creating Nodes and Relationships

```python
# Creating an entity node
session.run(
    """
    CREATE (n:Entity {name: $name})
    SET n += $sanitized_properties,
        n.title_embedding = $title_embedding,
        n.description_embedding = $description_embedding,
        n.category = $category,
        n.description = $description
    RETURN id(n) AS node_id
    """,
    name=node_name,
    sanitized_properties=sanitized_properties,
    title_embedding=title_embedding,
    description_embedding=description_embedding,
    category=category or "Miscellaneous",
    description=description,
)

# Creating a relationship between entities
session.run(
    """
    MATCH (s:Entity {name: $source})
    MATCH (t:Entity {name: $target})
    MERGE (s)-[r:`%s`]->(t)
    SET r += $rel_props
    """ % relationship_type,
    source=source,
    target=target,
    rel_props=relationship_properties,
)
```

## 5. Querying the Knowledge Graph

The system uses two main types of queries to retrieve information from the knowledge graph.

### 5.1 Semantic Search (Vector Similarity)

```python
def perform_similarity_search_rag(category, query, graph_driver, embed_model):
    """Perform similarity search in the graph for RAG."""
    # Generate embedding for the query
    query_embedding = embed_model.get_text_embedding(query)
    
    with graph_driver.session() as session:
        # Search for nodes in the specified category using vector similarity
        result = session.run(
            """
            MATCH (n:Entity)
            WHERE n.category = $category
            WITH n, gds.similarity.cosine(n.description_embedding, $query_embedding) AS score
            WHERE score > 0.5
            RETURN n, score
            ORDER BY score DESC
            LIMIT 5
            """,
            category=category,
            query_embedding=query_embedding,
        )
        
        # Process results
        nodes = []
        for record in result:
            node = record["n"]
            score = record["score"]
            nodes.append({
                "id": node.id,
                "properties": dict(node),
                "score": score,
            })
            
        # Return nodes and their relationships
        return {"nodes": nodes, "triplets": []}
```

This query:
1. Finds entities in a specific category
2. Calculates cosine similarity between the query embedding and entity embeddings
3. Returns entities with similarity scores above a threshold
4. Orders results by similarity score

### 5.2 Graph Traversal (Relationship-Based)

```python
def perform_similarity_search_crud(category, query, graph_driver, embed_model):
    """Perform similarity search for CRUD operations."""
    # Generate embedding for the query
    query_embedding = embed_model.get_text_embedding(query)
    
    with graph_driver.session() as session:
        # Find similar nodes
        similar_nodes_result = session.run(
            """
            MATCH (n:Entity)
            WHERE n.category = $category
            WITH n, gds.similarity.cosine(n.description_embedding, $query_embedding) AS score
            WHERE score > 0.5
            RETURN n, score
            ORDER BY score DESC
            LIMIT 5
            """,
            category=category,
            query_embedding=query_embedding,
        )
        
        # Get node IDs
        node_ids = [record["n"].id for record in similar_nodes_result]
        
        # Find relationships connected to these nodes
        if node_ids:
            relationships_result = session.run(
                """
                MATCH (n)-[r]-(m)
                WHERE id(n) IN $node_ids
                RETURN n, r, m
                """,
                node_ids=node_ids,
            )
            
            # Process relationships
            relationships = []
            for record in relationships_result:
                source = record["n"]
                rel = record["r"]
                target = record["m"]
                relationships.append({
                    "source": source.id,
                    "relationship": type(rel),
                    "target": target.id,
                    "properties": dict(rel),
                })
            
            return {"nodes": nodes, "relationships": relationships}
        else:
            return {"nodes": [], "relationships": []}
```

### 5.3 Example Neo4j Cypher Queries

#### Find all projects in the knowledge graph
```cypher
MATCH (c:Category {name: 'Projects'})-[r]->(e:Entity)
RETURN c, r, e
```

#### Find entities related to a specific project
```cypher
MATCH (e:Entity {name: 'Marketing Campaign 2023'})-[r]-(related)
RETURN e, r, related
```

#### Semantic search for marketing-related entities
```cypher
WITH $query_embedding AS query_embedding
MATCH (e:Entity)
WHERE e.category = 'Projects' OR e.category = 'Work'
WITH e, gds.similarity.cosine(e.description_embedding, query_embedding) AS score
WHERE score > 0.6
RETURN e, score
ORDER BY score DESC
LIMIT 5
```

#### Find connections between people and projects
```cypher
MATCH path = (p:Entity)-[r:WORKS_ON]->(proj:Entity)
WHERE p.category = 'Contacts' AND proj.category = 'Projects'
RETURN path
```

## 6. Complete Example Flow

Let's trace the flow of data from a Google Drive document to the knowledge graph:

### 6.1 Data Connection
The system authenticates with Google Drive API and searches for files.

### 6.2 Data Extraction
A document about a marketing project is found and downloaded:
```
File: "Marketing Campaign 2023 - Final Report.docx"
Content: "The Q1 2023 social media marketing campaign was led by Sarah from Marketing with support from John in Design. The project started on January 15, 2023 and was completed on March 30, 2023. Key deliverables included Instagram posts, Facebook ads, and a promotional video. The campaign resulted in a 25% increase in engagement and 15% increase in sales."
```

### 6.3 Text Categorization
The content is categorized using `text_dissection_runnable`:
```json
{
  "Projects": "Marketing Campaign 2023 is a social media initiative for Q1 2023...",
  "Contacts": "The project team includes Sarah from Marketing and John from Design...",
  "Timeline": "The project started on January 15, 2023 and was completed on March 30, 2023..."
}
```

### 6.4 Entity & Relationship Extraction
For each category, entities and relationships are extracted:
```json
[
  {
    "source": "Projects",
    "relationship": "HAS_PROJECT",
    "target": "Marketing Campaign 2023",
    "source_properties": {
      "description": "Work projects and initiatives"
    },
    "relationship_properties": {
      "type": "category_to_entity"
    },
    "target_properties": {
      "description": "Social media marketing campaign for Q1 2023",
      "status": "Completed",
      "document_link": "https://drive.google.com/file/d/abc123/view"
    }
  },
  {
    "source": "Marketing Campaign 2023",
    "relationship": "LED_BY",
    "target": "Sarah",
    "source_properties": {
      "description": "Social media marketing campaign for Q1 2023"
    },
    "relationship_properties": {
      "type": "entity_to_entity"
    },
    "target_properties": {
      "description": "Marketing team member",
      "department": "Marketing"
    }
  },
  {
    "source": "Marketing Campaign 2023",
    "relationship": "SUPPORTED_BY",
    "target": "John",
    "source_properties": {
      "description": "Social media marketing campaign for Q1 2023"
    },
    "relationship_properties": {
      "type": "entity_to_entity"
    },
    "target_properties": {
      "description": "Design team member",
      "department": "Design"
    }
  }
]
```

### 6.5 Knowledge Graph Construction
The extracted information is added to the Neo4j graph:
- A `Category` node for "Projects" is created or merged
- An `Entity` node for "Marketing Campaign 2023" is created with properties
- `Entity` nodes for "Sarah" and "John" are created with properties
- A `HAS_PROJECT` relationship is created from "Projects" to "Marketing Campaign 2023"
- A `LED_BY` relationship is created from "Marketing Campaign 2023" to "Sarah"
- A `SUPPORTED_BY` relationship is created from "Marketing Campaign 2023" to "John"

### 6.6 Resulting Knowledge Graph

The resulting knowledge graph would look like this in Neo4j:

```
(User:User {name: "User"})
(Projects:Category {name: "Projects", description: "Work projects and initiatives"})
(Contacts:Category {name: "Contacts", description: "People and relationships"})
(MarketingCampaign:Entity {
  name: "Marketing Campaign 2023",
  description: "Social media marketing campaign for Q1 2023",
  status: "Completed",
  document_link: "https://drive.google.com/file/d/abc123/view",
  category: "Projects",
  source: "Google Drive"
})
(Sarah:Entity {
  name: "Sarah",
  description: "Marketing team member",
  department: "Marketing",
  category: "Contacts",
  source: "Google Drive"
})
(John:Entity {
  name: "John",
  description: "Design team member",
  department: "Design",
  category: "Contacts",
  source: "Google Drive"
})

(User)-[:HAS_CATEGORY]->(Projects)
(User)-[:HAS_CATEGORY]->(Contacts)
(Projects)-[:HAS_PROJECT]->(MarketingCampaign)
(MarketingCampaign)-[:LED_BY]->(Sarah)
(MarketingCampaign)-[:SUPPORTED_BY]->(John)
```

This knowledge graph can now be queried to answer questions like:
- "What projects were completed in Q1 2023?"
- "Who led the Marketing Campaign 2023?"
- "What projects has Sarah worked on?"
- "What was the outcome of the Marketing Campaign 2023?"
