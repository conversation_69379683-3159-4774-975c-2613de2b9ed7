# Knowledge Graph Architecture: From Unstructured Data to Structured Knowledge

This document explains how the system connects to different data sources, extracts unstructured data, and forms a knowledge graph. It provides a step-by-step explanation of the architecture and processes involved, with examples from sources like Google Drive, Gmail, and Slack.

## 1. System Architecture Overview

The system follows a multi-layered architecture to transform unstructured data into a structured knowledge graph:

```
[Data Sources] → [Data Connectors] → [Data Extraction] → [Entity & Relationship Extraction] → [Knowledge Graph Construction]
```

## 2. Data Source Connections

The system connects to various external data sources through dedicated connectors. Each connector is responsible for authenticating with the service API and fetching data.

### 2.1 Gmail Connector Example

```python
class GmailContextEngine(BaseContextEngine):
    """Context Engine for processing Gmail data."""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.gmail_service = authenticate_gmail()  # Authenticate with Gmail API
        self.category = "gmail"  # Set data source category
        
    async def fetch_new_data(self):
        """Fetch new emails from Gmail since the last check."""
        # Query Gmail API for recent messages
        results = self.gmail_service.users().messages().list(
            userId="me", 
            maxResults=5, 
            q="in:inbox"
        ).execute()
        
        messages = results.get("messages", [])
        current_top_5_ids = [msg["id"] for msg in messages]
        
        # Compare with previously fetched IDs to identify new emails
        gmail_context = self.context.get("gmail", {})
        previous_top_5_ids = gmail_context.get("emails", {}).get("last_email_ids", [])
        new_email_ids = [eid for eid in current_top_5_ids if eid not in previous_top_5_ids]
        
        # Fetch full email content for new emails
        new_emails = []
        for email_id in new_email_ids:
            email = self.gmail_service.users().messages().get(
                userId="me", 
                id=email_id
            ).execute()
            new_emails.append(email)
            
        # Update context with latest email IDs
        self.context["gmail"] = {
            "last_updated": datetime.utcnow().isoformat() + "Z",
            "emails": {
                "last_email_ids": current_top_5_ids
            }
        }
        
        return new_emails
```

### 2.2 Google Calendar Connector Example

```python
class GCalendarContextEngine(BaseContextEngine):
    """Context Engine for processing Google Calendar data."""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.gcalendar_service = authenticate_calendar()  # Authenticate with Calendar API
        self.category = "gcalendar"  # Set data source category
        
    async def fetch_new_data(self):
        """Fetch upcoming events from Google Calendar."""
        # Set time range for event query (next 24 hours)
        now = datetime.utcnow()
        now_iso = now.isoformat() + "Z"
        time_max_iso = (now + timedelta(days=1)).isoformat() + "Z"
        
        # Query Calendar API for events
        events_result = self.gcalendar_service.events().list(
            calendarId="primary",
            timeMin=now_iso,
            timeMax=time_max_iso,
            maxResults=10,
            singleEvents=True,
            orderBy="startTime"
        ).execute()
        
        fetched_events = events_result.get("items", [])
        
        # Process and return events
        return fetched_events
```

### 2.3 Google Drive Connector Example

```python
async def search_file_in_gdrive(query: str) -> Dict[str, Any]:
    """Search for files in Google Drive matching the query."""
    try:
        service = authenticate_drive()  # Authenticate with Drive API
        
        # Search for files matching query
        results = service.files().list(
            q=f"name contains '{query}' and trashed=false",
            spaces="drive",
            fields="files(id, name, webViewLink, mimeType)"
        ).execute()
        
        files = results.get("files", [])
        return {
            "status": "success",
            "result": {
                "response": f"Found {len(files)} files matching '{query}'",
                "files": files
            }
        }
    except Exception as e:
        return {"status": "failure", "error": str(e)}
```

## 3. Data Extraction and Processing

Once data is fetched from external sources, it needs to be processed into a format suitable for knowledge graph construction.

### 3.1 Email Processing Example

```python
async def process_new_data(self, new_emails):
    """Process new emails into summaries."""
    summaries = [await self.generate_email_summary(email) for email in new_emails]
    joined_summaries = "\n".join(summaries)
    return joined_summaries

async def generate_email_summary(self, email):
    """Generate a summary for a single email."""
    snippet = email.get("snippet", "")
    subject = next((header["value"] for header in email["payload"]["headers"] 
                    if header["name"] == "Subject"), "No Subject")
    from_ = next((header["value"] for header in email["payload"]["headers"] 
                  if header["name"] == "From"), "Unknown Sender")
    
    summary = f"You have an email from {from_} with subject '{subject}' and snippet '{snippet}'"
    return summary
```

## 4. Entity and Relationship Extraction

The system uses LLM-based extraction to identify entities and relationships from unstructured text.

### 4.1 Entity Extraction Process

```python
def extract_entities_and_relationships(
    information: str, category: str, information_extraction_runnable
):
    """Extract entities and relationships from text for a given category."""
    try:
        # Use LLM-based runnable to extract structured information
        extracted_data = information_extraction_runnable.invoke({
            "category": category,
            "text": information
        })
        return extracted_data
    except Exception as e:
        print(f"Error extracting entities and relationships: {e}")
        return None
```

### 4.2 Interest Extraction Example

```python
async def extract_interests(self, context):
    """Extract user interests from context using an LLM runnable."""
    runnable = get_interest_extraction_runnable()
    try:
        # Invoke the runnable with the context
        interests = runnable.invoke({"context": context})
        # Validate the output format
        if isinstance(interests, list) and all(isinstance(item, str) for item in interests):
            return interests
        else:
            return []
    except Exception as e:
        return []
```

### 4.3 Keyword Extraction Example

```python
def extract_keywords(self, text: str) -> List[str]:
    """Extract keywords from text using NLP."""
    doc = nlp(text.lower())
    # Extract named entities
    keywords = [ent.text for ent in doc.ents]
    # Extract nouns and verbs
    keywords.extend([token.lemma_ for token in doc 
                    if token.pos_ in ['NOUN', 'VERB'] 
                    and not token.is_stop 
                    and len(token.text) > 2])
    # Remove duplicates
    unique_keywords = list(set(keywords))
    return unique_keywords
```

## 5. Knowledge Graph Construction

The system uses Neo4j as the graph database to store and query the knowledge graph.

### 5.1 Graph Structure

The knowledge graph consists of:
- **Nodes**: Represent entities (User, Category, Entity)
- **Relationships**: Connect nodes with typed relationships
- **Properties**: Store attributes of nodes and relationships
- **Embeddings**: Vector representations for semantic search

### 5.2 Node and Relationship Definition

```python
# Example of node creation in Neo4j
session.run(
    """
    CREATE (n:Entity {name: $name})
    SET n += $sanitized_properties,
        n.title_embedding = $title_embedding,
        n.description_embedding = $description_embedding,
        n.category = $category,
        n.description = $description
    RETURN id(n) AS node_id
    """,
    name=node_name,
    sanitized_properties=sanitized_properties,
    title_embedding=title_embedding,
    description_embedding=description_embedding,
    category=category or "Miscellaneous",
    description=description,
)

# Example of relationship creation
session.run(
    """
    MATCH (s:Entity {name: $source})
    MATCH (t:Entity {name: $target})
    MERGE (s)-[r:`%s`]->(t)
    SET r += $rel_props
    """ % relationship_type,
    source=source,
    target=target,
    rel_props=relationship_properties,
)
```

### 5.3 Building the Initial Knowledge Graph

```python
def build_initial_knowledge_graph(
    user_name: str,
    extracted_texts: list[dict],
    graph_driver,
    embed_model,
    text_dissection_runnable,
    information_extraction_runnable,
) -> str:
    """Build the initial knowledge graph from unstructured texts."""
    try:
        # Create predefined category nodes
        create_predefined_graph(user_name, CATEGORIES, graph_driver, embed_model)
        
        # Initialize dictionary to store categories and triplets
        categories_and_triplets = {}
        
        # Process each extracted text
        for text_data in extracted_texts:
            text = text_data["text"]
            source = text_data.get("source", "Unknown")
            
            # Dissect text into categories
            categorized_texts = text_dissection_runnable.invoke({
                "user_name": user_name,
                "text": text
            })
            
            # Process each category
            for category, text in categorized_texts.items():
                if not text:
                    continue
                    
                # Extract entities and relationships for this category
                triplets = extract_entities_and_relationships_for_category(
                    category, text, information_extraction_runnable
                )
                
                if triplets:
                    triplets["source"] = source
                    
                    if category not in categories_and_triplets:
                        categories_and_triplets[category] = {"triplets": []}
                        
                    categories_and_triplets[category]["triplets"].append(triplets)
        
        # Create graph with extracted categories and triplets
        graph_result = create_graph_with_categories(
            categories_and_triplets, graph_driver, embed_model, user_name, CATEGORIES
        )
        
        return graph_result
        
    except Exception as e:
        return "Failed to create graph due to an error."
```

## 6. Example: From Gmail to Knowledge Graph

Let's trace the flow of data from Gmail to the knowledge graph:

1. **Data Connection**: The `GmailContextEngine` authenticates with Gmail API and fetches new emails.

2. **Data Extraction**: Emails are processed to extract summaries with sender, subject, and content.

3. **Text Categorization**: The email content is categorized using `text_dissection_runnable` into predefined categories (e.g., Personal, Work, Events).

4. **Entity & Relationship Extraction**: For each category, entities and relationships are extracted:
   ```json
   {
     "source": "Events",
     "relationship": "HAS_EVENT",
     "target": "Team Meeting",
     "source_properties": {
       "description": "Calendar events and appointments"
     },
     "relationship_properties": {
       "type": "category_to_entity",
       "date": "2023-10-15"
     },
     "target_properties": {
       "description": "Weekly team sync meeting",
       "location": "Conference Room A"
     }
   }
   ```

5. **Knowledge Graph Construction**: Entities are created as nodes, relationships are established between them, and properties are set on both.

## 7. Example: From Google Drive to Knowledge Graph

1. **Data Connection**: The system authenticates with Google Drive API and fetches file metadata.

2. **Data Extraction**: File content is extracted based on file type (text, document, spreadsheet).

3. **Text Processing**: File content is processed to extract meaningful information.

4. **Entity & Relationship Extraction**: Entities (documents, topics, people) and their relationships are extracted.

5. **Knowledge Graph Construction**: The extracted information is added to the knowledge graph, creating nodes for documents and topics, and establishing relationships between them.

## 8. Querying the Knowledge Graph

The system uses a combination of semantic search and graph traversal to query the knowledge graph:

```python
def perform_similarity_search_rag(
    category: str, query: str, graph_driver, embed_model
) -> dict:
    """Perform similarity search in the graph for RAG."""
    query_embedding = embed_model.get_text_embedding(query)
    
    with graph_driver.session() as session:
        # Search for nodes in the specified category using vector similarity
        result = session.run(
            """
            MATCH (n:Entity)
            WHERE n.category = $category
            WITH n, gds.similarity.cosine(n.description_embedding, $query_embedding) AS score
            WHERE score > 0.5
            RETURN n, score
            ORDER BY score DESC
            LIMIT 5
            """,
            category=category,
            query_embedding=query_embedding,
        )
        
        # Process results
        nodes = []
        for record in result:
            node = record["n"]
            score = record["score"]
            nodes.append({
                "id": node.id,
                "properties": dict(node),
                "score": score,
            })
            
        # Return nodes and their relationships
        return {"nodes": nodes, "triplets": []}
```

## Conclusion

This architecture enables the system to:
1. Connect to diverse data sources (Gmail, Google Drive, Calendar, etc.)
2. Extract and process unstructured data
3. Identify entities and relationships using LLM-based extraction
4. Construct a knowledge graph with nodes, relationships, and properties
5. Query the knowledge graph for relevant information

The knowledge graph provides a structured representation of information that can be used for various applications, including question answering, recommendation, and decision support.
