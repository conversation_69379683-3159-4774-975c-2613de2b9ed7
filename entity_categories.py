"""
Entity Categories Module

This module defines categorization schemas for different entity types in an enterprise context.
Each entity type has its own set of relevant categories for knowledge graph integration.
"""

from typing import Dict, List, Any

# Common categories that apply to most entity types
COMMON_CATEGORIES = [
    {"name": "Description", "description": "General description or overview information"},
    {"name": "Key Attributes", "description": "Important characteristics or properties"},
    {"name": "Relationships", "description": "Connections to other entities"},
    {"name": "Timeline", "description": "Temporal information, dates, deadlines"},
    {"name": "Status", "description": "Current state or condition"},
    {"name": "Miscellaneous", "description": "Information that doesn't fit other categories"}
]

# Person entity categories
PERSON_CATEGORIES = [
    {"name": "Personal Information", "description": "Personal details, preferences, and biographical information"},
    {"name": "Work/Professional", "description": "Career information, job details, professional skills"},
    {"name": "Education", "description": "Academic background, courses, degrees, learning"},
    {"name": "Interests/Hobbies", "description": "Personal interests, hobbies, leisure activities"},
    {"name": "Projects", "description": "Work or personal projects, initiatives, and their details"},
    {"name": "Events/Calendar", "description": "Upcoming or past events, appointments, schedules"},
    {"name": "Contacts/Relationships", "description": "Information about people, relationships, connections"},
    {"name": "Miscellaneous", "description": "Information that doesn't fit into other categories"}
]

# Company entity categories
COMPANY_CATEGORIES = [
    {"name": "Company Overview", "description": "General information about the company"},
    {"name": "Industry", "description": "Business sector and market information"},
    {"name": "Products/Services", "description": "What the company offers"},
    {"name": "Leadership", "description": "Information about executives and management"},
    {"name": "Financials", "description": "Financial information and metrics"},
    {"name": "Employees", "description": "Information about staff and workforce"},
    {"name": "Partnerships", "description": "Business relationships and collaborations"},
    {"name": "Competitors", "description": "Information about competing companies"},
    {"name": "Miscellaneous", "description": "Other relevant information"}
]

# Project entity categories
PROJECT_CATEGORIES = [
    {"name": "Project Overview", "description": "General information about the project"},
    {"name": "Objectives", "description": "Goals and intended outcomes"},
    {"name": "Timeline", "description": "Schedule, milestones, deadlines"},
    {"name": "Team Members", "description": "People involved in the project"},
    {"name": "Resources", "description": "Budget, tools, and other resources"},
    {"name": "Status", "description": "Current state and progress"},
    {"name": "Deliverables", "description": "Expected outputs and results"},
    {"name": "Risks/Issues", "description": "Problems, challenges, and mitigation strategies"},
    {"name": "Miscellaneous", "description": "Other relevant information"}
]

# Jira issue categories
JIRA_ISSUE_CATEGORIES = [
    {"name": "Issue Summary", "description": "Brief description of the issue"},
    {"name": "Description", "description": "Detailed explanation of the issue"},
    {"name": "Status", "description": "Current state (open, in progress, resolved, etc.)"},
    {"name": "Priority", "description": "Importance level"},
    {"name": "Assignee", "description": "Person responsible for the issue"},
    {"name": "Reporter", "description": "Person who reported the issue"},
    {"name": "Comments", "description": "Discussion and updates"},
    {"name": "Attachments", "description": "References to attached files"},
    {"name": "Related Issues", "description": "Links to other issues"},
    {"name": "Timeline", "description": "Creation date, due date, updates"},
    {"name": "Miscellaneous", "description": "Other relevant information"}
]

# Slack conversation categories
SLACK_CATEGORIES = [
    {"name": "Conversation Summary", "description": "Overview of the discussion"},
    {"name": "Participants", "description": "People involved in the conversation"},
    {"name": "Key Points", "description": "Important information shared"},
    {"name": "Decisions", "description": "Conclusions or choices made"},
    {"name": "Action Items", "description": "Tasks to be completed"},
    {"name": "Questions", "description": "Inquiries raised during the conversation"},
    {"name": "Links/Resources", "description": "Shared URLs and references"},
    {"name": "Timeline", "description": "When the conversation occurred"},
    {"name": "Miscellaneous", "description": "Other relevant information"}
]

# Google Drive document categories
GDRIVE_DOCUMENT_CATEGORIES = [
    {"name": "Document Summary", "description": "Overview of the document content"},
    {"name": "Author", "description": "Creator of the document"},
    {"name": "Contributors", "description": "People who edited or commented"},
    {"name": "Key Points", "description": "Important information in the document"},
    {"name": "Sections", "description": "Main parts of the document"},
    {"name": "References", "description": "Citations and external sources"},
    {"name": "Creation/Modification", "description": "Dates and timeline information"},
    {"name": "Related Documents", "description": "Links to other relevant files"},
    {"name": "Miscellaneous", "description": "Other relevant information"}
]

# Meeting categories
MEETING_CATEGORIES = [
    {"name": "Meeting Overview", "description": "General information about the meeting"},
    {"name": "Attendees", "description": "People who participated"},
    {"name": "Agenda", "description": "Topics discussed"},
    {"name": "Discussion Points", "description": "Details of what was discussed"},
    {"name": "Decisions", "description": "Conclusions reached"},
    {"name": "Action Items", "description": "Tasks assigned during the meeting"},
    {"name": "Next Steps", "description": "Follow-up activities"},
    {"name": "Timeline", "description": "Date, time, duration"},
    {"name": "Miscellaneous", "description": "Other relevant information"}
]

# Map entity types to their category sets
ENTITY_CATEGORY_MAP = {
    "Person": PERSON_CATEGORIES,
    "Company": COMPANY_CATEGORIES,
    "Project": PROJECT_CATEGORIES,
    "JiraIssue": JIRA_ISSUE_CATEGORIES,
    "SlackConversation": SLACK_CATEGORIES,
    "GDriveDocument": GDRIVE_DOCUMENT_CATEGORIES,
    "Meeting": MEETING_CATEGORIES
}

# Map data sources to likely entity types
DATA_SOURCE_ENTITY_MAP = {
    "pdf": ["Person", "Company", "Project", "Meeting"],
    "slack": ["SlackConversation", "Person", "Project"],
    "jira": ["JiraIssue", "Person", "Project"],
    "gdrive": ["GDriveDocument", "Person", "Project", "Company"],
    "email": ["Person", "Project", "Meeting"],
    "confluence": ["GDriveDocument", "Project", "Meeting"]
}

def get_categories_for_entity_type(entity_type: str) -> List[Dict[str, str]]:
    """
    Get the appropriate categories for a specific entity type.
    
    Args:
        entity_type: The type of entity (e.g., "Person", "Company", "JiraIssue")
        
    Returns:
        A list of category dictionaries with name and description
    """
    return ENTITY_CATEGORY_MAP.get(entity_type, COMMON_CATEGORIES)

def get_likely_entity_types(data_source: str) -> List[str]:
    """
    Get likely entity types for a specific data source.
    
    Args:
        data_source: The source of the data (e.g., "pdf", "slack", "jira")
        
    Returns:
        A list of likely entity types for this data source
    """
    return DATA_SOURCE_ENTITY_MAP.get(data_source.lower(), ["Person", "Project"])
